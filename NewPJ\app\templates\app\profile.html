{% extends 'app/base.html' %}

{% block content %}
<div class="profile-container">
    <h2>Your Profile</h2>
    
    <div class="profile-stats">
        <div class="stat-card">
            <i class="fas fa-book"></i>
            <h3>{{ entry_count }}</h3>
            <p>Journal Entries</p>
        </div>
        <div class="stat-card">
            <i class="fas fa-fire"></i>
            <h3>{{ streak.current_streak }}</h3>
            <p>Current Streak</p>
        </div>
        <div class="stat-card">
            <i class="fas fa-trophy"></i>
            <h3>{{ streak.longest_streak }}</h3>
            <p>Longest Streak</p>
        </div>
    </div>
    
    <div class="profile-forms">
        <div class="form-section">
            <h3>Update Profile</h3>
            <form method="post">
                {% csrf_token %}
                {% for field in form %}
                <div class="form-group">
                    <label for="{{ field.id_for_label }}">{{ field.label }}</label>
                    {{ field }}
                    {% if field.errors %}
                    <div class="error-message">{{ field.errors }}</div>
                    {% endif %}
                </div>
                {% endfor %}
                <button type="submit" name="update_profile">Update Profile</button>
            </form>
        </div>
        
        <div class="form-section">
            <h3>Change Password</h3>
            <form method="post">
                {% csrf_token %}
                {% for field in password_form %}
                <div class="form-group">
                    <label for="{{ field.id_for_label }}">{{ field.label }}</label>
                    {{ field }}
                    {% if field.errors %}
                    <div class="error-message">{{ field.errors }}</div>
                    {% endif %}
                </div>
                {% endfor %}
                <button type="submit" name="change_password">Change Password</button>
            </form>
        </div>
    </div>
</div>
{% endblock %}