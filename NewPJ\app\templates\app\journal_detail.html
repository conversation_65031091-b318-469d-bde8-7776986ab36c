{% extends 'app/base.html' %}

{% block content %}
<div class="journal-detail">
    <div class="journal-header">
        <h2>{{ entry.title }}</h2>
        <div class="entry-meta">
            <span class="date"><i class="far fa-calendar-alt animated-icon"></i> {{ entry.created_at|date:"F j, Y" }}</span>
            {% if entry.category %}
            <span class="category"><i class="fas fa-folder animated-icon"></i> {{ entry.category }}</span>
            {% endif %}
        </div>
    </div>
    
    {% if entry.image %}
    <div class="entry-image" onclick="openModal('{{ entry.image.url }}')">
        <img src="{{ entry.image.url }}" alt="{{ entry.title }}">
    </div>
    {% endif %}
    
    <div class="entry-content">
        {{ entry.content|linebreaks }}
    </div>
    
    {% if entry.audio %}
    <div class="entry-audio">
        <h4><i class="fas fa-headphones animated-icon"></i> Audio Recording</h4>
        <audio controls>
            <source src="{{ entry.audio.url }}" type="audio/mpeg">
            Your browser does not support the audio element.
        </audio>
    </div>
    {% endif %}
    
    {% if entry.tags %}
    <div class="entry-tags">
        {% for tag in entry.tags.split %}
        <span class="tag"><i class="fas fa-tag animated-icon"></i> {{ tag }}</span>
        {% endfor %}
    </div>
    {% endif %}
    
    <div class="entry-actions">
        <a href="{% url 'journal_edit' pk=entry.pk %}" class="button"><i class="fas fa-edit animated-icon"></i> Edit</a>
        <a href="{% url 'journal_delete' pk=entry.pk %}" class="button danger" onclick="return confirm('Are you sure you want to delete this entry?')"><i class="fas fa-trash animated-icon"></i> Delete</a>
        <a href="{% url 'journal_list' %}" class="button secondary"><i class="fas fa-arrow-left animated-icon"></i> Back to List</a>
    </div>
</div>
{% endblock %}




