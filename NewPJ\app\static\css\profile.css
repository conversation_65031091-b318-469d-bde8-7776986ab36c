/* Profile Page Styles */

/* Profile Container */
.profile-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: var(--space-6);
}

/* Profile Header */
.profile-header {
  background: var(--bg-surface);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  margin-bottom: var(--space-8);
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-xl);
  position: relative;
  overflow: hidden;
}

.profile-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
  box-shadow: var(--glow-primary);
}

.profile-header::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-mesh);
  opacity: 0.05;
  z-index: -1;
}

.profile-info {
  display: flex;
  align-items: center;
  gap: var(--space-6);
  margin-bottom: var(--space-6);
}

.profile-avatar {
  width: 6rem;
  height: 6rem;
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
  font-size: var(--font-size-3xl);
  font-weight: 700;
  box-shadow: var(--shadow-xl), var(--glow-primary);
  position: relative;
  overflow: hidden;
}

.profile-avatar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
  animation: avatarShimmer 3s infinite;
}

@keyframes avatarShimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.profile-details h1 {
  font-size: var(--font-size-3xl);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--space-2);
  font-weight: 800;
}

.profile-email {
  color: var(--text-secondary);
  font-size: var(--font-size-lg);
  margin-bottom: var(--space-3);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.profile-email i {
  color: var(--accent-color);
}

.profile-joined {
  color: var(--text-muted);
  font-size: var(--font-size-sm);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.profile-joined i {
  color: var(--primary-color);
}

/* Profile Stats */
.profile-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-8);
}

.profile-stat {
  background: var(--bg-surface);
  border-radius: var(--radius-xl);
  padding: var(--space-5);
  border: 1px solid var(--border-primary);
  text-align: center;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.profile-stat::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: var(--radius-xl);
  padding: 1px;
  background: var(--gradient-accent);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.profile-stat:hover::before {
  opacity: 1;
}

.profile-stat:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl), var(--glow-accent);
}

.profile-stat-icon {
  width: 3rem;
  height: 3rem;
  background: var(--gradient-accent);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
  font-size: var(--font-size-xl);
  margin: 0 auto var(--space-3);
  box-shadow: var(--shadow-md), var(--glow-accent);
}

.profile-stat-number {
  font-size: var(--font-size-2xl);
  font-weight: 800;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.profile-stat-label {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-weight: 600;
}

/* Profile Sections */
.profile-sections {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-6);
}

.profile-section {
  background: var(--bg-surface);
  border-radius: var(--radius-2xl);
  padding: var(--space-6);
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-lg);
}

.profile-section h2 {
  font-size: var(--font-size-xl);
  color: var(--text-primary);
  margin-bottom: var(--space-4);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.profile-section h2 i {
  color: var(--primary-color);
}

/* Recent Activity */
.activity-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-3);
  transition: all var(--transition-normal);
}

.activity-item:hover {
  background: var(--bg-elevated);
}

.activity-icon {
  width: 2.5rem;
  height: 2.5rem;
  background: var(--gradient-secondary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
}

.activity-title {
  color: var(--text-primary);
  font-weight: 600;
  margin-bottom: var(--space-1);
  font-size: var(--font-size-sm);
}

.activity-time {
  color: var(--text-muted);
  font-size: var(--font-size-xs);
}

/* Settings Section */
.settings-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-3);
  transition: all var(--transition-normal);
}

.settings-item:hover {
  background: var(--bg-elevated);
}

.settings-label {
  color: var(--text-primary);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.settings-label i {
  color: var(--accent-color);
}

.settings-toggle {
  width: 3rem;
  height: 1.5rem;
  background: var(--bg-elevated);
  border-radius: var(--radius-full);
  position: relative;
  cursor: pointer;
  transition: all var(--transition-normal);
  border: 1px solid var(--border-primary);
}

.settings-toggle.active {
  background: var(--gradient-primary);
  border-color: var(--primary-color);
}

.settings-toggle::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 1rem;
  height: 1rem;
  background: var(--text-primary);
  border-radius: var(--radius-full);
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.settings-toggle.active::after {
  transform: translateX(1.5rem);
}

/* Profile Actions */
.profile-actions {
  display: flex;
  gap: var(--space-3);
  margin-top: var(--space-6);
  justify-content: center;
}

.profile-action-btn {
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  font-weight: 600;
  text-decoration: none;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  cursor: pointer;
  border: none;
}

.profile-action-btn.primary {
  background: var(--gradient-primary);
  color: var(--text-primary);
  box-shadow: var(--shadow-md), var(--glow-primary);
}

.profile-action-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl), var(--glow-primary);
}

.profile-action-btn.secondary {
  background: var(--bg-elevated);
  color: var(--text-secondary);
  border: 1px solid var(--border-primary);
}

.profile-action-btn.secondary:hover {
  background: var(--bg-overlay);
  color: var(--primary-color);
  border-color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-container {
    padding: var(--space-4);
  }
  
  .profile-info {
    flex-direction: column;
    text-align: center;
    gap: var(--space-4);
  }
  
  .profile-sections {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .profile-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .profile-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .profile-header {
    padding: var(--space-6);
  }
  
  .profile-avatar {
    width: 5rem;
    height: 5rem;
    font-size: var(--font-size-2xl);
  }
  
  .profile-details h1 {
    font-size: var(--font-size-2xl);
  }
  
  .profile-stats {
    grid-template-columns: 1fr;
  }
  
  .profile-section {
    padding: var(--space-4);
  }
}
