/* Templates Page Styles */

/* Templates Container */
.templates-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-6);
}

/* Templates Header */
.templates-header {
  text-align: center;
  margin-bottom: var(--space-8);
  padding: var(--space-8) 0;
  background: var(--gradient-bg-surface);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--border-primary);
  position: relative;
  overflow: hidden;
}

.templates-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-secondary);
  box-shadow: var(--glow-secondary);
}

.templates-header h1 {
  font-size: var(--font-size-4xl);
  background: var(--gradient-secondary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--space-4);
  font-weight: 800;
  animation: titleGlow 3s ease-in-out infinite alternate;
}

.templates-header p {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* Templates Actions */
.templates-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
  padding: var(--space-4);
  background: var(--bg-surface);
  border-radius: var(--radius-xl);
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-md);
}

.templates-search {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  flex: 1;
  max-width: 400px;
}

.templates-search input {
  flex: 1;
  padding: var(--space-3) var(--space-4);
  background: var(--bg-elevated);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  font-size: var(--font-size-base);
  transition: all var(--transition-normal);
}

.templates-search input:focus {
  outline: none;
  border-color: var(--secondary-color);
  box-shadow: var(--glow-secondary);
}

.templates-search i {
  color: var(--text-muted);
  font-size: var(--font-size-lg);
}

.templates-filter {
  display: flex;
  gap: var(--space-2);
  margin: 0 var(--space-4);
}

.filter-btn {
  padding: var(--space-2) var(--space-4);
  background: var(--bg-elevated);
  color: var(--text-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
  text-decoration: none;
}

.filter-btn:hover,
.filter-btn.active {
  background: var(--gradient-secondary);
  color: var(--text-primary);
  border-color: transparent;
  box-shadow: var(--shadow-sm), var(--glow-secondary);
}

.create-template-btn {
  padding: var(--space-3) var(--space-6);
  background: var(--gradient-secondary);
  color: var(--text-primary);
  border: none;
  border-radius: var(--radius-lg);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-md), var(--glow-secondary);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.create-template-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl), var(--glow-secondary);
}

/* Templates Grid */
.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

/* Template Card */
.template-card {
  background: var(--bg-surface);
  border-radius: var(--radius-2xl);
  padding: var(--space-6);
  border: 1px solid var(--border-primary);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  box-shadow: var(--shadow-lg);
}

.template-card::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: var(--radius-2xl);
  padding: 1px;
  background: var(--gradient-secondary);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.template-card:hover::before {
  opacity: 1;
}

.template-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl), var(--glow-secondary);
}

/* Template Card Header */
.template-card-header {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.template-icon {
  width: 3rem;
  height: 3rem;
  background: var(--gradient-secondary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
  font-size: var(--font-size-xl);
  box-shadow: var(--shadow-md), var(--glow-secondary);
}

.template-card-title {
  flex: 1;
}

.template-card-title h3 {
  font-size: var(--font-size-xl);
  color: var(--text-primary);
  margin-bottom: var(--space-1);
  font-weight: 700;
}

.template-category {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-weight: 600;
  background: var(--bg-elevated);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
}

/* Template Card Content */
.template-card-content {
  margin-bottom: var(--space-4);
}

.template-description {
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
  margin-bottom: var(--space-3);
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.template-preview {
  background: var(--bg-elevated);
  border-radius: var(--radius-lg);
  padding: var(--space-3);
  border: 1px solid var(--border-primary);
  font-family: 'Courier New', monospace;
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  max-height: 100px;
  overflow: hidden;
  position: relative;
}

.template-preview::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(transparent, var(--bg-elevated));
}

.template-stats {
  display: flex;
  gap: var(--space-4);
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  margin-top: var(--space-3);
}

.template-stat {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.template-stat i {
  color: var(--secondary-color);
}

/* Template Card Actions */
.template-card-actions {
  display: flex;
  gap: var(--space-2);
  margin-top: auto;
}

.template-action-btn {
  flex: 1;
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  background: var(--bg-elevated);
  color: var(--text-secondary);
  text-decoration: none;
  text-align: center;
  font-size: var(--font-size-sm);
  font-weight: 500;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-1);
  cursor: pointer;
}

.template-action-btn:hover {
  background: var(--bg-overlay);
  color: var(--secondary-color);
  border-color: var(--secondary-color);
  transform: translateY(-1px);
}

.template-action-btn.primary {
  background: var(--gradient-secondary);
  color: var(--text-primary);
  border-color: transparent;
}

.template-action-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg), var(--glow-secondary);
}

/* Featured Templates */
.featured-templates {
  margin-bottom: var(--space-8);
}

.featured-header {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-6);
}

.featured-header h2 {
  font-size: var(--font-size-2xl);
  color: var(--text-primary);
  margin: 0;
}

.featured-badge {
  background: var(--gradient-secondary);
  color: var(--text-primary);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  box-shadow: var(--shadow-sm), var(--glow-secondary);
}

/* Empty State */
.templates-empty {
  text-align: center;
  padding: var(--space-12);
  background: var(--bg-surface);
  border-radius: var(--radius-2xl);
  border: 2px dashed var(--border-primary);
  margin: var(--space-8) 0;
}

.templates-empty-icon {
  font-size: var(--font-size-6xl);
  color: var(--text-muted);
  margin-bottom: var(--space-4);
  opacity: 0.5;
}

.templates-empty h3 {
  font-size: var(--font-size-2xl);
  color: var(--text-secondary);
  margin-bottom: var(--space-2);
}

.templates-empty p {
  color: var(--text-muted);
  margin-bottom: var(--space-6);
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
  .templates-container {
    padding: var(--space-4);
  }
  
  .templates-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .templates-actions {
    flex-direction: column;
    gap: var(--space-4);
  }
  
  .templates-search {
    max-width: none;
  }
  
  .templates-filter {
    justify-content: center;
    flex-wrap: wrap;
    margin: 0;
  }
  
  .templates-header {
    padding: var(--space-6) var(--space-4);
  }
  
  .templates-header h1 {
    font-size: var(--font-size-3xl);
  }
}

@media (max-width: 480px) {
  .template-card {
    padding: var(--space-4);
  }
  
  .template-card-actions {
    flex-direction: column;
  }
  
  .template-action-btn {
    flex: none;
  }
  
  .featured-header {
    flex-direction: column;
    text-align: center;
    gap: var(--space-2);
  }
}
