/* Journal List Page Styles */

/* <PERSON> Header */
.journal-header {
  text-align: center;
  margin-bottom: var(--space-8);
  padding: var(--space-8) 0;
  background: var(--gradient-bg-surface);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--border-primary);
  position: relative;
  overflow: hidden;
}

.journal-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-primary);
  box-shadow: var(--glow-primary);
}

.journal-header h1 {
  font-size: var(--font-size-4xl);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--space-4);
  font-weight: 800;
  animation: titleGlow 3s ease-in-out infinite alternate;
}

.journal-header p {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* Action Bar */
.journal-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
  padding: var(--space-4);
  background: var(--bg-surface);
  border-radius: var(--radius-xl);
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-md);
}

.journal-search {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  flex: 1;
  max-width: 400px;
}

.journal-search input {
  flex: 1;
  padding: var(--space-3) var(--space-4);
  background: var(--bg-elevated);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  font-size: var(--font-size-base);
  transition: all var(--transition-normal);
}

.journal-search input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: var(--glow-primary);
}

.journal-search i {
  color: var(--text-muted);
  font-size: var(--font-size-lg);
}

.journal-create-btn {
  padding: var(--space-3) var(--space-6);
  background: var(--gradient-primary);
  color: var(--text-primary);
  border: none;
  border-radius: var(--radius-lg);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-md), var(--glow-primary);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.journal-create-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl), var(--glow-primary);
}

/* Journal Grid */
.journal-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

/* Journal Card */
.journal-card {
  background: var(--bg-surface);
  border-radius: var(--radius-2xl);
  padding: var(--space-6);
  border: 1px solid var(--border-primary);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  box-shadow: var(--shadow-lg);
}

.journal-card::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: var(--radius-2xl);
  padding: 1px;
  background: var(--gradient-primary);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.journal-card:hover::before {
  opacity: 1;
}

.journal-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl), var(--glow-primary);
}

/* Journal Card Header */
.journal-card-header {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.journal-icon {
  width: 3rem;
  height: 3rem;
  background: var(--gradient-accent);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
  font-size: var(--font-size-xl);
  box-shadow: var(--shadow-md), var(--glow-accent);
}

.journal-card-title {
  flex: 1;
}

.journal-card-title h3 {
  font-size: var(--font-size-xl);
  color: var(--text-primary);
  margin-bottom: var(--space-1);
  font-weight: 700;
}

.journal-card-date {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

/* Journal Card Content */
.journal-card-content {
  margin-bottom: var(--space-4);
}

.journal-card-description {
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
  margin-bottom: var(--space-3);
}

.journal-card-stats {
  display: flex;
  gap: var(--space-4);
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

.journal-stat {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.journal-stat i {
  color: var(--primary-color);
}

/* Journal Card Actions */
.journal-card-actions {
  display: flex;
  gap: var(--space-2);
  margin-top: auto;
}

.journal-action-btn {
  flex: 1;
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  background: var(--bg-elevated);
  color: var(--text-secondary);
  text-decoration: none;
  text-align: center;
  font-size: var(--font-size-sm);
  font-weight: 500;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-1);
}

.journal-action-btn:hover {
  background: var(--bg-overlay);
  color: var(--primary-color);
  border-color: var(--primary-color);
  transform: translateY(-1px);
}

.journal-action-btn.primary {
  background: var(--gradient-primary);
  color: var(--text-primary);
  border-color: transparent;
}

.journal-action-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg), var(--glow-primary);
}

/* Empty State */
.journal-empty {
  text-align: center;
  padding: var(--space-12);
  background: var(--bg-surface);
  border-radius: var(--radius-2xl);
  border: 2px dashed var(--border-primary);
  margin: var(--space-8) 0;
}

.journal-empty-icon {
  font-size: var(--font-size-6xl);
  color: var(--text-muted);
  margin-bottom: var(--space-4);
  opacity: 0.5;
}

.journal-empty h3 {
  font-size: var(--font-size-2xl);
  color: var(--text-secondary);
  margin-bottom: var(--space-2);
}

.journal-empty p {
  color: var(--text-muted);
  margin-bottom: var(--space-6);
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
  .journal-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .journal-actions {
    flex-direction: column;
    gap: var(--space-4);
  }
  
  .journal-search {
    max-width: none;
  }
  
  .journal-header {
    padding: var(--space-6) var(--space-4);
  }
  
  .journal-header h1 {
    font-size: var(--font-size-3xl);
  }
}

@media (max-width: 480px) {
  .journal-card {
    padding: var(--space-4);
  }
  
  .journal-card-actions {
    flex-direction: column;
  }
  
  .journal-action-btn {
    flex: none;
  }
  
  .journal-header {
    margin-bottom: var(--space-6);
  }
}
