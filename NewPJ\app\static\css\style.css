/* Dark Theme - Complete CSS Replacement with your color palette */

/* Base Variables */
:root {
  --bg-color: #4a1a4a;        /* Deep purple */
  --secondary-bg: #6b5b4a;    /* Olive brown */
  --text-color: #f5f5dc;      /* Cream */
  --accent-color: #ff6b47;    /* Orange */
  --error-color: #ff6b47;     /* Using accent for errors */
  --success-color: #6b5b4a;   /* Using secondary for success */
  --border-color: #6b5b4a;    /* Using secondary for borders */
  --shadow-color: rgba(0, 0, 0, 0.3);
}

/* Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Base Styles */
body {
  background-color: var(--bg-color);
  color: var(--text-color);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.6;
  min-height: 100vh;
}

.container {
  width: 90%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin-bottom: 1rem;
  font-weight: 600;
  line-height: 1.2;
  color: var(--text-color);
}

p {
  margin-bottom: 1rem;
}

a {
  color: var(--accent-color);
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  text-decoration: underline;
  color: var(--text-color);
}

/* Header */
header {
  background-color: var(--bg-color);
  padding: 1rem 0;
  box-shadow: 0 2px 10px var(--shadow-color);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
}

.logo h1 {
  font-size: 1.5rem;
  margin-left: 0.5rem;
  margin-bottom: 0;
}

nav {
  display: flex;
  gap: 1.5rem;
}

nav a {
  color: var(--text-color);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

nav a:hover {
  color: var(--accent-color);
  text-decoration: none;
}

/* Main Content */
main {
  padding: 2rem 0;
  min-height: calc(100vh - 160px);
}

/* Footer */
footer {
  background-color: var(--secondary-bg);
  padding: 1.5rem 0;
  margin-top: 2rem;
}

.footer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 1rem;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-links a {
  color: var(--text-color);
  font-size: 1.2rem;
}

.social-links a:hover {
  color: var(--accent-color);
}

/* Cards */
.card, .journal-detail, .entry-card, .auth-container {
  background-color: var(--secondary-bg);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px var(--shadow-color);
  border: 1px solid var(--border-color);
}

.entry-card {
  display: flex;
  flex-direction: column;
}

.entry-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
  border-radius: 8px;
  margin-bottom: 1rem;
  cursor: pointer;
}

.entry-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.entry-image:hover img {
  transform: scale(1.05);
}

.entry-title {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.entry-date {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.entry-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
}

/* Forms */
form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

label {
  font-weight: 500;
}

input, textarea, select {
  padding: 0.8rem;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  background-color: var(--bg-color);
  color: var(--text-color);
  font-family: inherit;
  font-size: 1rem;
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--accent-color);
}

/* Buttons */
button, .button, input[type="submit"] {
  padding: 0.8rem 1.5rem;
  border-radius: 4px;
  border: none;
  background-color: var(--accent-color);
  color: var(--text-color);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

button:hover, .button:hover, input[type="submit"]:hover {
  background-color: #a370db;
}

.button.small {
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
}

.button.danger {
  background-color: var(--error-color);
}

.button.danger:hover {
  background-color: #b55a68;
}

/* Utilities */
.page-title {
  margin-bottom: 2rem;
  font-size: 2rem;
  text-align: center;
}

.error-message {
  color: var(--error-color);
  margin-bottom: 1rem;
}

.success-message {
  color: var(--success-color);
  margin-bottom: 1rem;
}

/* Animations */
.animated-icon {
  transition: transform 0.3s ease;
}

a:hover .animated-icon, button:hover .animated-icon {
  transform: scale(1.2);
}

/* Modal */
.image-modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  overflow: auto;
}

.image-modal.active {
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-content {
  max-width: 90%;
  max-height: 90%;
  object-fit: contain;
}

.close-modal {
  position: absolute;
  top: 15px;
  right: 35px;
  color: var(--text-color);
  font-size: 40px;
  font-weight: bold;
  cursor: pointer;
}

/* Particles */
.particles-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
}

.particle {
  position: absolute;
  background-color: var(--accent-color);
  opacity: 0.3;
  border-radius: 50%;
  animation: float 20s infinite linear;
}

@keyframes float {
  0% {
    transform: translateY(0) translateX(0);
  }
  25% {
    transform: translateY(-20vh) translateX(20vw);
  }
  50% {
    transform: translateY(-40vh) translateX(0);
  }
  75% {
    transform: translateY(-20vh) translateX(-20vw);
  }
  100% {
    transform: translateY(0) translateX(0);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1rem;
  }
  
  nav {
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .entry-list {
    grid-template-columns: 1fr;
  }
}

/* Remove theme switcher */
.theme-switcher {
  display: none;
}




