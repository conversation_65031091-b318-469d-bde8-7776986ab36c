/* Modern Dark Theme System - No White Colors */

/* Core Dark Theme Variables */
:root {
  /* Primary Color Palette - Deep Blues & Purples */
  --primary-color: #6366f1;        /* Indigo */
  --primary-dark: #4f46e5;         /* Darker indigo */
  --primary-light: #818cf8;        /* Lighter indigo */

  /* Secondary Colors - Vibrant Accents */
  --secondary-color: #8b5cf6;      /* Purple */
  --accent-color: #06b6d4;         /* Cyan */
  --accent-secondary: #10b981;     /* Emerald */

  /* Status Colors */
  --success-color: #22c55e;        /* Green */
  --warning-color: #f59e0b;        /* Amber */
  --error-color: #ef4444;          /* Red */
  --info-color: #3b82f6;           /* Blue */

  /* Background System - Dark Grays & Blues */
  --bg-primary: #0a0e1a;           /* Deep navy */
  --bg-secondary: #1a1f2e;         /* Dark slate */
  --bg-tertiary: #2d3748;          /* Medium slate */
  --bg-surface: #1e2532;           /* Card background */
  --bg-elevated: #252d3d;          /* Elevated surfaces */
  --bg-overlay: #374151;           /* Overlay background */

  /* Text Color System */
  --text-primary: #f1f5f9;         /* Light gray */
  --text-secondary: #cbd5e1;       /* Medium gray */
  --text-muted: #94a3b8;           /* Muted gray */
  --text-disabled: #64748b;        /* Disabled text */
  --text-inverse: #0f172a;         /* Dark text for light backgrounds */

  /* Border & Divider System */
  --border-primary: #374151;       /* Primary borders */
  --border-secondary: #4b5563;     /* Secondary borders */
  --border-accent: #6366f1;        /* Accent borders */
  --divider-color: #374151;        /* Dividers */

  /* Enhanced Shadow System */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.4);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6), 0 10px 10px -5px rgba(0, 0, 0, 0.5);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.8);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.3);

  /* Glow Effects */
  --glow-primary: 0 0 20px rgba(99, 102, 241, 0.3);
  --glow-secondary: 0 0 20px rgba(139, 92, 246, 0.3);
  --glow-accent: 0 0 20px rgba(6, 182, 212, 0.3);
  --glow-success: 0 0 20px rgba(34, 197, 94, 0.3);
  --glow-error: 0 0 20px rgba(239, 68, 68, 0.3);

  /* Advanced Gradient System */
  --gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #a855f7 100%);
  --gradient-secondary: linear-gradient(135deg, #8b5cf6 0%, #ec4899 50%, #f97316 100%);
  --gradient-accent: linear-gradient(135deg, #06b6d4 0%, #3b82f6 50%, #6366f1 100%);
  --gradient-success: linear-gradient(135deg, #10b981 0%, #22c55e 50%, #84cc16 100%);
  --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #f97316 50%, #ef4444 100%);
  --gradient-error: linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #991b1b 100%);

  /* Background Gradients */
  --gradient-bg-primary: linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 50%, #2d3748 100%);
  --gradient-bg-secondary: linear-gradient(135deg, #1a1f2e 0%, #2d3748 50%, #374151 100%);
  --gradient-bg-surface: linear-gradient(135deg, #1e2532 0%, #252d3d 50%, #374151 100%);

  /* Animated Gradients */
  --gradient-animated: linear-gradient(-45deg, #6366f1, #8b5cf6, #06b6d4, #10b981);
  --gradient-mesh: radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.3) 0%, transparent 50%),
                   radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.3) 0%, transparent 50%),
                   radial-gradient(circle at 40% 40%, rgba(6, 182, 212, 0.2) 0%, transparent 50%);

  /* Border Radius System */
  --radius-xs: 0.125rem;
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-full: 9999px;

  /* Spacing System */
  --space-px: 1px;
  --space-0: 0;
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;

  /* Animation & Transition System */
  --transition-fast: 0.1s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slower: 0.5s cubic-bezier(0.4, 0, 0.2, 1);

  /* Easing Functions */
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Z-Index Scale */
  --z-base: 0;
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;

  /* Typography Scale */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  --font-size-6xl: 3.75rem;

  /* Line Heights */
  --leading-none: 1;
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;
}

/* Enhanced Reset */
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

/* Advanced Base Styles */
body {
  background: var(--gradient-bg-primary);
  color: var(--text-primary);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: var(--leading-relaxed);
  min-height: 100vh;
  font-size: var(--font-size-base);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: all var(--transition-normal);
  position: relative;
  overflow-x: hidden;
}

/* Animated Background Mesh */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-mesh);
  pointer-events: none;
  z-index: -2;
  opacity: 0.6;
  animation: meshFloat 20s ease-in-out infinite;
}

/* Animated Gradient Background */
body::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 400%;
  height: 400%;
  background: var(--gradient-animated);
  background-size: 400% 400%;
  pointer-events: none;
  z-index: -3;
  opacity: 0.1;
  animation: gradientShift 15s ease infinite;
}

@keyframes meshFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-20px) rotate(2deg);
    opacity: 0.8;
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Enhanced Container System */
.container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--space-4);
  position: relative;
}

.container-sm {
  max-width: 640px;
}

.container-md {
  max-width: 768px;
}

.container-lg {
  max-width: 1024px;
}

.container-xl {
  max-width: 1280px;
}

.container-2xl {
  max-width: 1536px;
}

/* Advanced Typography System */
h1, h2, h3, h4, h5, h6 {
  margin-bottom: var(--space-4);
  font-weight: 700;
  line-height: var(--leading-tight);
  color: var(--text-primary);
  letter-spacing: -0.025em;
  transition: all var(--transition-normal);
  position: relative;
}

h1 {
  font-size: var(--font-size-5xl);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
  text-shadow: var(--glow-primary);
  animation: titleGlow 3s ease-in-out infinite alternate;
}

h2 {
  font-size: var(--font-size-4xl);
  color: var(--text-primary);
  text-shadow: var(--shadow-sm);
}

h3 {
  font-size: var(--font-size-3xl);
  background: var(--gradient-accent);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

h4 {
  font-size: var(--font-size-2xl);
  color: var(--text-primary);
}

h5 {
  font-size: var(--font-size-xl);
  color: var(--text-secondary);
}

h6 {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

@keyframes titleGlow {
  0% {
    filter: drop-shadow(0 0 5px rgba(99, 102, 241, 0.3));
  }
  100% {
    filter: drop-shadow(0 0 20px rgba(99, 102, 241, 0.6));
  }
}

p {
  margin-bottom: var(--space-4);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
  transition: color var(--transition-normal);
  font-size: var(--font-size-base);
}

/* Enhanced Link Styles */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: all var(--transition-fast);
  font-weight: 500;
  position: relative;
}

a:hover {
  color: var(--primary-light);
  text-shadow: var(--glow-primary);
}

/* Glowing link hover effect */
a:not(.button):not(.nav-link):not(.social-link)::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--gradient-accent);
  transition: width var(--transition-normal);
  box-shadow: var(--glow-accent);
}

a:not(.button):not(.nav-link):not(.social-link):hover::after {
  width: 100%;
}

/* Text Selection */
::selection {
  background: var(--primary-color);
  color: var(--text-inverse);
}

::-moz-selection {
  background: var(--primary-color);
  color: var(--text-inverse);
}

/* Futuristic Header */
header {
  background: rgba(26, 31, 46, 0.95);
  backdrop-filter: blur(20px);
  padding: var(--space-4) 0;
  box-shadow: var(--shadow-xl);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  border-bottom: 1px solid var(--border-primary);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

/* Header glow effect */
header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--gradient-primary);
  box-shadow: var(--glow-primary);
}

/* Header animated background */
header::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
  animation: headerSweep 3s ease-in-out infinite;
}

@keyframes headerSweep {
  0% { left: -100%; }
  50% { left: 100%; }
  100% { left: -100%; }
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 var(--space-4);
  position: relative;
  z-index: 1;
}

/* Enhanced Logo */
.logo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
}

.logo:hover {
  transform: scale(1.05);
  filter: drop-shadow(var(--glow-primary));
}

.logo h1 {
  font-size: var(--font-size-2xl);
  margin: 0;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
  text-shadow: var(--glow-primary);
}

.logo i {
  font-size: var(--font-size-2xl);
  color: var(--primary-color);
  filter: drop-shadow(var(--glow-primary));
  animation: logoFloat 3s ease-in-out infinite;
  transition: all var(--transition-normal);
}

.logo:hover i {
  animation-play-state: paused;
  transform: rotate(360deg) scale(1.1);
}

@keyframes logoFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-5px) rotate(5deg);
  }
}

/* Futuristic Navigation */
nav {
  display: flex;
  gap: var(--space-6);
  align-items: center;
}

.nav-link {
  color: var(--text-primary);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-xl);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  text-decoration: none;
  border: 1px solid transparent;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
}

/* Nav link glow effect */
.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gradient-accent);
  transition: left var(--transition-normal);
  z-index: -1;
  opacity: 0.2;
}

.nav-link::after {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: var(--radius-xl);
  padding: 1px;
  background: var(--gradient-primary);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.nav-link:hover::before {
  left: 0;
}

.nav-link:hover::after {
  opacity: 1;
}

.nav-link:hover {
  color: var(--primary-light);
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg), var(--glow-primary);
  border-color: var(--primary-color);
}

.nav-link i {
  transition: all var(--transition-fast);
  font-size: var(--font-size-lg);
}

.nav-link:hover i {
  transform: scale(1.2) rotate(10deg);
  color: var(--primary-light);
  filter: drop-shadow(var(--glow-primary));
}

/* Advanced Theme Switcher */
.theme-switcher {
  display: flex !important;
  align-items: center;
  gap: var(--space-1);
  background: var(--bg-surface);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-full);
  padding: var(--space-1);
  transition: all var(--transition-normal);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.theme-switcher::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: var(--radius-full);
  padding: 2px;
  background: var(--gradient-primary);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.theme-switcher:hover::before {
  opacity: 1;
}

.theme-switcher:hover {
  box-shadow: var(--shadow-lg), var(--glow-primary);
  transform: scale(1.05);
}

.theme-toggle {
  background: none;
  border: none;
  padding: var(--space-2);
  border-radius: var(--radius-full);
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-muted);
  position: relative;
  width: 2.5rem;
  height: 2.5rem;
}

.theme-toggle.active {
  background: var(--gradient-primary);
  color: var(--text-primary);
  box-shadow: var(--shadow-sm), var(--glow-primary);
  transform: scale(1.1);
}

.theme-toggle i {
  font-size: var(--font-size-base);
  transition: all var(--transition-normal);
}

.theme-toggle:hover i {
  transform: scale(1.2) rotate(15deg);
}

.theme-toggle.active i {
  animation: themeIconSpin 0.5s ease-in-out;
}

@keyframes themeIconSpin {
  0% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.2); }
  100% { transform: rotate(360deg) scale(1); }
}

/* Main Content */
main {
  padding: 3rem 0;
  min-height: calc(100vh - 200px);
}

/* Enhanced Footer */
footer {
  background: var(--card-bg);
  border-top: 1px solid var(--border-color);
  padding: var(--space-2xl) 0 var(--space-xl);
  margin-top: var(--space-2xl);
  position: relative;
  overflow: hidden;
}

footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-xl);
  margin-bottom: var(--space-xl);
}

.footer-section {
  text-align: center;
}

.footer-section h4 {
  color: var(--text-color);
  margin-bottom: var(--space-md);
  font-size: 1.125rem;
  font-weight: 700;
}

.footer-section p {
  color: var(--text-secondary);
  margin-bottom: var(--space-sm);
  line-height: 1.6;
}

.footer-links {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
  align-items: center;
}

.footer-links a {
  color: var(--text-secondary);
  transition: all var(--transition-normal);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
}

.footer-links a:hover {
  color: var(--primary-color);
  background: var(--secondary-bg);
  transform: translateX(5px);
}

/* Enhanced Social Links */
.social-links {
  display: flex;
  gap: var(--space-md);
  justify-content: center;
  flex-wrap: wrap;
}

.social-link {
  color: var(--text-muted);
  font-size: 1.25rem;
  padding: var(--space-sm);
  border-radius: var(--radius-full);
  background: var(--secondary-bg);
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  position: relative;
  overflow: hidden;
  text-decoration: none;
}

.social-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-primary);
  transform: scale(0);
  transition: transform var(--transition-normal);
  border-radius: var(--radius-full);
  z-index: -1;
}

.social-link:hover::before {
  transform: scale(1);
}

.social-link:hover {
  color: var(--text-inverse);
  transform: translateY(-3px) scale(1.1);
  box-shadow: var(--shadow-lg);
}

.social-link i {
  transition: transform var(--transition-normal);
}

.social-link:hover i {
  transform: scale(1.2) rotate(10deg);
}

/* Footer Bottom */
.footer-bottom {
  border-top: 1px solid var(--border-color);
  padding-top: var(--space-md);
  text-align: center;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--space-md);
}

.footer-bottom p {
  color: var(--text-muted);
  margin: 0;
  font-size: 0.875rem;
}

.footer-bottom .footer-links {
  flex-direction: row;
  gap: var(--space-lg);
}

.footer-bottom .footer-links a {
  font-size: 0.875rem;
  padding: var(--space-xs);
}

/* Back to Top Button */
.back-to-top {
  position: fixed;
  bottom: var(--space-xl);
  right: var(--space-xl);
  background: var(--gradient-primary);
  color: var(--text-inverse);
  border: none;
  border-radius: var(--radius-full);
  width: 3.5rem;
  height: 3.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-fixed);
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
}

.back-to-top.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.back-to-top:hover {
  transform: translateY(-5px) scale(1.1);
  box-shadow: var(--shadow-xl);
}

.back-to-top i {
  font-size: 1.25rem;
  transition: transform var(--transition-fast);
}

.back-to-top:hover i {
  transform: translateY(-2px);
}

/* Cards */
.card, .journal-detail, .entry-card, .auth-container {
  background: var(--bg-color);
  border-radius: var(--border-radius-xl);
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease-in-out;
  position: relative;
  overflow: hidden;
}

.card::before, .journal-detail::before, .entry-card::before, .auth-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
}

.card:hover, .journal-detail:hover, .entry-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
}

.entry-card {
  display: flex;
  flex-direction: column;
}

.entry-image {
  width: 100%;
  height: 250px;
  overflow: hidden;
  border-radius: var(--border-radius-lg);
  margin-bottom: 1.5rem;
  cursor: pointer;
  position: relative;
}

.entry-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.4s ease-in-out;
}

.entry-image:hover img {
  transform: scale(1.1);
}

.entry-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 0%, rgba(37, 99, 235, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.entry-image:hover::after {
  opacity: 1;
}

.entry-title {
  font-size: 1.5rem;
  margin-bottom: 0.75rem;
  color: var(--text-color);
  font-weight: 700;
}

.entry-date {
  color: var(--text-muted);
  font-size: 0.875rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.entry-date i {
  color: var(--primary-color);
}

.entry-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: auto;
  padding-top: 1rem;
}

/* Forms */
form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

label {
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

input, textarea, select {
  padding: 1rem;
  border-radius: var(--border-radius-md);
  border: 2px solid var(--border-color);
  background-color: var(--bg-color);
  color: var(--text-color);
  font-family: inherit;
  font-size: 1rem;
  transition: all 0.2s ease-in-out;
  box-shadow: var(--shadow-sm);
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  transform: translateY(-2px);
}

textarea {
  resize: vertical;
  min-height: 120px;
}

/* Buttons */
button, .button, input[type="submit"] {
  padding: 1rem 2rem;
  border-radius: var(--border-radius-md);
  border: none;
  background: var(--gradient-primary);
  color: var(--bg-color);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  font-size: 1rem;
  text-decoration: none;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

button::before, .button::before, input[type="submit"]::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s ease;
}

button:hover::before, .button:hover::before, input[type="submit"]:hover::before {
  left: 100%;
}

button:hover, .button:hover, input[type="submit"]:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-xl);
}

.button.small {
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
}

.button.secondary {
  background: var(--secondary-bg);
  color: var(--text-color);
  border: 2px solid var(--border-color);
}

.button.secondary:hover {
  background: var(--tertiary-bg);
  border-color: var(--primary-color);
}

.button.danger {
  background: linear-gradient(135deg, var(--error-color), #dc2626);
}

.button.danger:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
}

.button.success {
  background: linear-gradient(135deg, var(--success-color), #059669);
}

.button.success:hover {
  background: linear-gradient(135deg, #059669, #047857);
}

/* Utilities */
.page-title {
  margin-bottom: 3rem;
  font-size: 3rem;
  text-align: center;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
  letter-spacing: -0.02em;
}

.error-message {
  color: var(--error-color);
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  padding: 1rem;
  border-radius: var(--border-radius-md);
  margin-bottom: 1.5rem;
  font-weight: 500;
}

.success-message {
  color: var(--success-color);
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
  padding: 1rem;
  border-radius: var(--border-radius-md);
  margin-bottom: 1.5rem;
  font-weight: 500;
}

/* Animations */
.animated-icon {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

a:hover .animated-icon, button:hover .animated-icon {
  transform: scale(1.2) rotate(5deg);
}

/* Floating Animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

/* Pulse Animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.pulse-animation {
  animation: pulse 2s ease-in-out infinite;
}

/* Modal */
.image-modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(10px);
  overflow: auto;
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.image-modal.active {
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-content {
  max-width: 90%;
  max-height: 90%;
  object-fit: contain;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  animation: modalSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(-50px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.close-modal {
  position: absolute;
  top: 2rem;
  right: 2rem;
  color: var(--bg-color);
  font-size: 2rem;
  font-weight: bold;
  cursor: pointer;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-modal:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(1.1) rotate(90deg);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .container {
    padding: 0 1.5rem;
  }

  .page-title {
    font-size: 2.5rem;
  }

  nav {
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1.5rem;
    padding: 0 1rem;
  }

  nav {
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
  }

  nav a {
    padding: 0.75rem 1.25rem;
    font-size: 0.875rem;
  }

  .page-title {
    font-size: 2rem;
    margin-bottom: 2rem;
  }

  .card, .journal-detail, .entry-card, .auth-container {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .entry-image {
    height: 200px;
  }

  button, .button, input[type="submit"] {
    padding: 0.875rem 1.5rem;
    font-size: 0.875rem;
  }

  .entry-list {
    grid-template-columns: 1fr;
  }

  main {
    padding: 2rem 0;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 1rem;
  }

  .logo h1 {
    font-size: 1.5rem;
  }

  .page-title {
    font-size: 1.75rem;
  }

  .card, .journal-detail, .entry-card, .auth-container {
    padding: 1rem;
  }

  .entry-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  button, .button, input[type="submit"] {
    width: 100%;
    justify-content: center;
  }

  .social-links a {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1.25rem;
  }
}

/* Remove theme switcher */
.theme-switcher {
  display: none !important;
}

/* Additional Modern Elements */
.gradient-accent {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-primary);
  opacity: 0.03;
  z-index: -2;
  pointer-events: none;
}

.particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  pointer-events: none;
  overflow: hidden;
}

.particle {
  position: absolute;
  background: var(--primary-color);
  border-radius: 50%;
  opacity: 0.1;
  animation: particleFloat 15s infinite linear;
}