/* Modern Light Theme - Complete CSS Redesign */

/* Base Variables */
:root {
  --primary-color: #2563eb;      /* Modern blue */
  --secondary-color: #1e40af;    /* Darker blue */
  --accent-color: #06b6d4;       /* Cyan accent */
  --success-color: #10b981;      /* Emerald green */
  --warning-color: #f59e0b;      /* Amber */
  --error-color: #ef4444;        /* Red */
  --bg-color: #ffffff;           /* Pure white */
  --secondary-bg: #f8fafc;       /* Light gray */
  --tertiary-bg: #f1f5f9;        /* Lighter gray */
  --text-color: #1e293b;         /* Dark slate */
  --text-secondary: #64748b;     /* Medium slate */
  --text-muted: #94a3b8;         /* Light slate */
  --border-color: #e2e8f0;       /* Light border */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-accent: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);
  --border-radius-sm: 0.375rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;
}

/* Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Base Styles */
body {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  color: var(--text-color);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.6;
  min-height: 100vh;
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.container {
  width: 90%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin-bottom: 1rem;
  font-weight: 700;
  line-height: 1.2;
  color: var(--text-color);
  letter-spacing: -0.025em;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
  margin-bottom: 1rem;
  color: var(--text-secondary);
  line-height: 1.7;
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: all 0.2s ease-in-out;
  font-weight: 500;
}

a:hover {
  color: var(--secondary-color);
  text-decoration: none;
}

/* Header */
header {
  background: var(--bg-color);
  backdrop-filter: blur(20px);
  padding: 1rem 0;
  box-shadow: var(--shadow-md);
  position: sticky;
  top: 0;
  z-index: 100;
  border-bottom: 1px solid var(--border-color);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 1rem;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.logo h1 {
  font-size: 1.75rem;
  margin: 0;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
}

.logo i {
  font-size: 2rem;
  color: var(--primary-color);
  filter: drop-shadow(0 2px 4px rgba(37, 99, 235, 0.2));
}

nav {
  display: flex;
  gap: 2rem;
  align-items: center;
}

nav a {
  color: var(--text-color);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-md);
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
}

nav a::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gradient-accent);
  transition: left 0.3s ease-in-out;
  z-index: -1;
  opacity: 0.1;
}

nav a:hover::before {
  left: 0;
}

nav a:hover {
  color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* Main Content */
main {
  padding: 3rem 0;
  min-height: calc(100vh - 200px);
}

/* Footer */
footer {
  background: var(--bg-color);
  border-top: 1px solid var(--border-color);
  padding: 2rem 0;
  margin-top: 3rem;
}

.footer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 1.5rem;
}

.footer-content p {
  color: var(--text-muted);
  margin: 0;
}

.social-links {
  display: flex;
  gap: 1.5rem;
}

.social-links a {
  color: var(--text-muted);
  font-size: 1.5rem;
  padding: 0.75rem;
  border-radius: 50%;
  background: var(--secondary-bg);
  transition: all 0.3s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
}

.social-links a:hover {
  color: var(--bg-color);
  background: var(--primary-color);
  transform: translateY(-3px) scale(1.1);
  box-shadow: var(--shadow-lg);
}

/* Cards */
.card, .journal-detail, .entry-card, .auth-container {
  background: var(--bg-color);
  border-radius: var(--border-radius-xl);
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease-in-out;
  position: relative;
  overflow: hidden;
}

.card::before, .journal-detail::before, .entry-card::before, .auth-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
}

.card:hover, .journal-detail:hover, .entry-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
}

.entry-card {
  display: flex;
  flex-direction: column;
}

.entry-image {
  width: 100%;
  height: 250px;
  overflow: hidden;
  border-radius: var(--border-radius-lg);
  margin-bottom: 1.5rem;
  cursor: pointer;
  position: relative;
}

.entry-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.4s ease-in-out;
}

.entry-image:hover img {
  transform: scale(1.1);
}

.entry-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 0%, rgba(37, 99, 235, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.entry-image:hover::after {
  opacity: 1;
}

.entry-title {
  font-size: 1.5rem;
  margin-bottom: 0.75rem;
  color: var(--text-color);
  font-weight: 700;
}

.entry-date {
  color: var(--text-muted);
  font-size: 0.875rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.entry-date i {
  color: var(--primary-color);
}

.entry-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: auto;
  padding-top: 1rem;
}

/* Forms */
form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

label {
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

input, textarea, select {
  padding: 1rem;
  border-radius: var(--border-radius-md);
  border: 2px solid var(--border-color);
  background-color: var(--bg-color);
  color: var(--text-color);
  font-family: inherit;
  font-size: 1rem;
  transition: all 0.2s ease-in-out;
  box-shadow: var(--shadow-sm);
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  transform: translateY(-2px);
}

textarea {
  resize: vertical;
  min-height: 120px;
}

/* Buttons */
button, .button, input[type="submit"] {
  padding: 1rem 2rem;
  border-radius: var(--border-radius-md);
  border: none;
  background: var(--gradient-primary);
  color: var(--bg-color);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  font-size: 1rem;
  text-decoration: none;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

button::before, .button::before, input[type="submit"]::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s ease;
}

button:hover::before, .button:hover::before, input[type="submit"]:hover::before {
  left: 100%;
}

button:hover, .button:hover, input[type="submit"]:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-xl);
}

.button.small {
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
}

.button.secondary {
  background: var(--secondary-bg);
  color: var(--text-color);
  border: 2px solid var(--border-color);
}

.button.secondary:hover {
  background: var(--tertiary-bg);
  border-color: var(--primary-color);
}

.button.danger {
  background: linear-gradient(135deg, var(--error-color), #dc2626);
}

.button.danger:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
}

.button.success {
  background: linear-gradient(135deg, var(--success-color), #059669);
}

.button.success:hover {
  background: linear-gradient(135deg, #059669, #047857);
}

/* Utilities */
.page-title {
  margin-bottom: 3rem;
  font-size: 3rem;
  text-align: center;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
  letter-spacing: -0.02em;
}

.error-message {
  color: var(--error-color);
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  padding: 1rem;
  border-radius: var(--border-radius-md);
  margin-bottom: 1.5rem;
  font-weight: 500;
}

.success-message {
  color: var(--success-color);
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
  padding: 1rem;
  border-radius: var(--border-radius-md);
  margin-bottom: 1.5rem;
  font-weight: 500;
}

/* Animations */
.animated-icon {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

a:hover .animated-icon, button:hover .animated-icon {
  transform: scale(1.2) rotate(5deg);
}

/* Floating Animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

/* Pulse Animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.pulse-animation {
  animation: pulse 2s ease-in-out infinite;
}

/* Modal */
.image-modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(10px);
  overflow: auto;
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.image-modal.active {
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-content {
  max-width: 90%;
  max-height: 90%;
  object-fit: contain;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  animation: modalSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(-50px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.close-modal {
  position: absolute;
  top: 2rem;
  right: 2rem;
  color: var(--bg-color);
  font-size: 2rem;
  font-weight: bold;
  cursor: pointer;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-modal:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(1.1) rotate(90deg);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .container {
    padding: 0 1.5rem;
  }

  .page-title {
    font-size: 2.5rem;
  }

  nav {
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1.5rem;
    padding: 0 1rem;
  }

  nav {
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
  }

  nav a {
    padding: 0.75rem 1.25rem;
    font-size: 0.875rem;
  }

  .page-title {
    font-size: 2rem;
    margin-bottom: 2rem;
  }

  .card, .journal-detail, .entry-card, .auth-container {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .entry-image {
    height: 200px;
  }

  button, .button, input[type="submit"] {
    padding: 0.875rem 1.5rem;
    font-size: 0.875rem;
  }

  .entry-list {
    grid-template-columns: 1fr;
  }

  main {
    padding: 2rem 0;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 1rem;
  }

  .logo h1 {
    font-size: 1.5rem;
  }

  .page-title {
    font-size: 1.75rem;
  }

  .card, .journal-detail, .entry-card, .auth-container {
    padding: 1rem;
  }

  .entry-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  button, .button, input[type="submit"] {
    width: 100%;
    justify-content: center;
  }

  .social-links a {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1.25rem;
  }
}

/* Remove theme switcher */
.theme-switcher {
  display: none !important;
}

/* Additional Modern Elements */
.gradient-accent {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-primary);
  opacity: 0.03;
  z-index: -2;
  pointer-events: none;
}

.particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  pointer-events: none;
  overflow: hidden;
}

.particle {
  position: absolute;
  background: var(--primary-color);
  border-radius: 50%;
  opacity: 0.1;
  animation: particleFloat 15s infinite linear;
}

.particle:nth-child(1) { width: 4px; height: 4px; left: 10%; animation-delay: 0s; }
.particle:nth-child(2) { width: 6px; height: 6px; left: 20%; animation-delay: 2s; }
.particle:nth-child(3) { width: 3px; height: 3px; left: 30%; animation-delay: 4s; }
.particle:nth-child(4) { width: 5px; height: 5px; left: 40%; animation-delay: 6s; }
.particle:nth-child(5) { width: 4px; height: 4px; left: 50%; animation-delay: 8s; }
.particle:nth-child(6) { width: 7px; height: 7px; left: 60%; animation-delay: 10s; }
.particle:nth-child(7) { width: 3px; height: 3px; left: 70%; animation-delay: 12s; }

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.1;
  }
  90% {
    opacity: 0.1;
  }
  100% {
    transform: translateY(-100vh) rotate(360deg);
    opacity: 0;
  }
}




