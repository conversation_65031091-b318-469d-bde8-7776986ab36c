/* Advanced Theme System - Light & Dark Mode Support */

/* Light Theme Variables (Default) */
:root {
  /* Core Colors */
  --primary-color: #2563eb;
  --secondary-color: #1e40af;
  --accent-color: #06b6d4;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #3b82f6;

  /* Background Colors */
  --bg-color: #ffffff;
  --secondary-bg: #f8fafc;
  --tertiary-bg: #f1f5f9;
  --card-bg: #ffffff;
  --surface-bg: #fafbfc;

  /* Text Colors */
  --text-color: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
  --text-inverse: #ffffff;

  /* Border & Divider Colors */
  --border-color: #e2e8f0;
  --border-light: #f1f5f9;
  --divider-color: #e5e7eb;

  /* Shadow System */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Gradient System */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-accent: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);
  --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
  --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  --gradient-error: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  --gradient-bg: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);

  /* Border Radius System */
  --radius-xs: 0.25rem;
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;

  /* Spacing System */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;

  /* Animation Timing */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;

  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* Dark Theme Variables */
[data-theme="dark"] {
  /* Core Colors - Adjusted for dark mode */
  --primary-color: #3b82f6;
  --secondary-color: #1d4ed8;
  --accent-color: #06b6d4;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #60a5fa;

  /* Background Colors */
  --bg-color: #0f172a;
  --secondary-bg: #1e293b;
  --tertiary-bg: #334155;
  --card-bg: #1e293b;
  --surface-bg: #0f172a;

  /* Text Colors */
  --text-color: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --text-inverse: #0f172a;

  /* Border & Divider Colors */
  --border-color: #334155;
  --border-light: #475569;
  --divider-color: #475569;

  /* Shadow System - Enhanced for dark mode */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.4);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6), 0 10px 10px -5px rgba(0, 0, 0, 0.5);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.8);

  /* Gradient System - Dark mode variants */
  --gradient-primary: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  --gradient-secondary: linear-gradient(135deg, #ec4899 0%, #be185d 100%);
  --gradient-accent: linear-gradient(135deg, #06b6d4 0%, #0284c7 100%);
  --gradient-bg: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

/* Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Base Styles */
body {
  background: var(--gradient-bg);
  color: var(--text-color);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.6;
  min-height: 100vh;
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background var(--transition-normal), color var(--transition-normal);
  position: relative;
  overflow-x: hidden;
}

/* Enhanced Background Effects */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(37, 99, 235, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
  opacity: 0.7;
  transition: opacity var(--transition-normal);
}

[data-theme="dark"] body::before {
  background:
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(124, 58, 237, 0.1) 0%, transparent 50%);
  opacity: 0.5;
}

.container {
  width: 90%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-md);
}

/* Enhanced Typography */
h1, h2, h3, h4, h5, h6 {
  margin-bottom: var(--space-md);
  font-weight: 700;
  line-height: 1.2;
  color: var(--text-color);
  letter-spacing: -0.025em;
  transition: color var(--transition-normal);
}

h1 {
  font-size: clamp(2rem, 5vw, 3rem);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
}

h2 { font-size: clamp(1.75rem, 4vw, 2.5rem); }
h3 { font-size: clamp(1.5rem, 3vw, 2rem); }
h4 { font-size: clamp(1.25rem, 2.5vw, 1.5rem); }
h5 { font-size: clamp(1.125rem, 2vw, 1.25rem); }
h6 { font-size: 1rem; }

p {
  margin-bottom: var(--space-md);
  color: var(--text-secondary);
  line-height: 1.7;
  transition: color var(--transition-normal);
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: all var(--transition-fast);
  font-weight: 500;
  position: relative;
}

a:hover {
  color: var(--secondary-color);
  text-decoration: none;
}

/* Enhanced link hover effect */
a:not(.button):not(.nav-link)::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--gradient-accent);
  transition: width var(--transition-normal);
}

a:not(.button):not(.nav-link):hover::after {
  width: 100%;
}

/* Enhanced Header */
header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  padding: var(--space-md) 0;
  box-shadow: var(--shadow-lg);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  border-bottom: 1px solid var(--border-color);
  transition: all var(--transition-normal);
}

[data-theme="dark"] header {
  background: rgba(15, 23, 42, 0.95);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 var(--space-md);
  position: relative;
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  cursor: pointer;
  transition: transform var(--transition-normal);
}

.logo:hover {
  transform: scale(1.05);
}

.logo h1 {
  font-size: clamp(1.5rem, 3vw, 2rem);
  margin: 0;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
}

.logo i {
  font-size: clamp(1.5rem, 3vw, 2rem);
  color: var(--primary-color);
  filter: drop-shadow(0 2px 4px rgba(37, 99, 235, 0.3));
  animation: logoFloat 3s ease-in-out infinite;
}

@keyframes logoFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
}

/* Enhanced Navigation */
nav {
  display: flex;
  gap: var(--space-lg);
  align-items: center;
}

.nav-link {
  color: var(--text-color);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  text-decoration: none;
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gradient-accent);
  transition: left var(--transition-normal);
  z-index: -1;
  opacity: 0.1;
}

.nav-link:hover::before {
  left: 0;
}

.nav-link:hover {
  color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.nav-link i {
  transition: transform var(--transition-fast);
}

.nav-link:hover i {
  transform: scale(1.2) rotate(5deg);
}

/* Theme Switcher */
.theme-switcher {
  display: flex !important;
  align-items: center;
  gap: var(--space-sm);
  background: var(--secondary-bg);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-full);
  padding: var(--space-xs);
  transition: all var(--transition-normal);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.theme-switcher:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
}

.theme-toggle {
  background: none;
  border: none;
  padding: var(--space-sm);
  border-radius: var(--radius-full);
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-muted);
  position: relative;
}

.theme-toggle.active {
  background: var(--gradient-primary);
  color: var(--text-inverse);
  box-shadow: var(--shadow-sm);
}

.theme-toggle i {
  font-size: 1rem;
  transition: transform var(--transition-normal);
}

.theme-toggle:hover i {
  transform: scale(1.1) rotate(10deg);
}

/* Main Content */
main {
  padding: 3rem 0;
  min-height: calc(100vh - 200px);
}

/* Enhanced Footer */
footer {
  background: var(--card-bg);
  border-top: 1px solid var(--border-color);
  padding: var(--space-2xl) 0 var(--space-xl);
  margin-top: var(--space-2xl);
  position: relative;
  overflow: hidden;
}

footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-xl);
  margin-bottom: var(--space-xl);
}

.footer-section {
  text-align: center;
}

.footer-section h4 {
  color: var(--text-color);
  margin-bottom: var(--space-md);
  font-size: 1.125rem;
  font-weight: 700;
}

.footer-section p {
  color: var(--text-secondary);
  margin-bottom: var(--space-sm);
  line-height: 1.6;
}

.footer-links {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
  align-items: center;
}

.footer-links a {
  color: var(--text-secondary);
  transition: all var(--transition-normal);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
}

.footer-links a:hover {
  color: var(--primary-color);
  background: var(--secondary-bg);
  transform: translateX(5px);
}

/* Enhanced Social Links */
.social-links {
  display: flex;
  gap: var(--space-md);
  justify-content: center;
  flex-wrap: wrap;
}

.social-link {
  color: var(--text-muted);
  font-size: 1.25rem;
  padding: var(--space-sm);
  border-radius: var(--radius-full);
  background: var(--secondary-bg);
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  position: relative;
  overflow: hidden;
  text-decoration: none;
}

.social-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-primary);
  transform: scale(0);
  transition: transform var(--transition-normal);
  border-radius: var(--radius-full);
  z-index: -1;
}

.social-link:hover::before {
  transform: scale(1);
}

.social-link:hover {
  color: var(--text-inverse);
  transform: translateY(-3px) scale(1.1);
  box-shadow: var(--shadow-lg);
}

.social-link i {
  transition: transform var(--transition-normal);
}

.social-link:hover i {
  transform: scale(1.2) rotate(10deg);
}

/* Footer Bottom */
.footer-bottom {
  border-top: 1px solid var(--border-color);
  padding-top: var(--space-md);
  text-align: center;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--space-md);
}

.footer-bottom p {
  color: var(--text-muted);
  margin: 0;
  font-size: 0.875rem;
}

.footer-bottom .footer-links {
  flex-direction: row;
  gap: var(--space-lg);
}

.footer-bottom .footer-links a {
  font-size: 0.875rem;
  padding: var(--space-xs);
}

/* Back to Top Button */
.back-to-top {
  position: fixed;
  bottom: var(--space-xl);
  right: var(--space-xl);
  background: var(--gradient-primary);
  color: var(--text-inverse);
  border: none;
  border-radius: var(--radius-full);
  width: 3.5rem;
  height: 3.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-fixed);
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
}

.back-to-top.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.back-to-top:hover {
  transform: translateY(-5px) scale(1.1);
  box-shadow: var(--shadow-xl);
}

.back-to-top i {
  font-size: 1.25rem;
  transition: transform var(--transition-fast);
}

.back-to-top:hover i {
  transform: translateY(-2px);
}

/* Cards */
.card, .journal-detail, .entry-card, .auth-container {
  background: var(--bg-color);
  border-radius: var(--border-radius-xl);
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease-in-out;
  position: relative;
  overflow: hidden;
}

.card::before, .journal-detail::before, .entry-card::before, .auth-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
}

.card:hover, .journal-detail:hover, .entry-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
}

.entry-card {
  display: flex;
  flex-direction: column;
}

.entry-image {
  width: 100%;
  height: 250px;
  overflow: hidden;
  border-radius: var(--border-radius-lg);
  margin-bottom: 1.5rem;
  cursor: pointer;
  position: relative;
}

.entry-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.4s ease-in-out;
}

.entry-image:hover img {
  transform: scale(1.1);
}

.entry-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 0%, rgba(37, 99, 235, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.entry-image:hover::after {
  opacity: 1;
}

.entry-title {
  font-size: 1.5rem;
  margin-bottom: 0.75rem;
  color: var(--text-color);
  font-weight: 700;
}

.entry-date {
  color: var(--text-muted);
  font-size: 0.875rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.entry-date i {
  color: var(--primary-color);
}

.entry-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: auto;
  padding-top: 1rem;
}

/* Forms */
form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

label {
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

input, textarea, select {
  padding: 1rem;
  border-radius: var(--border-radius-md);
  border: 2px solid var(--border-color);
  background-color: var(--bg-color);
  color: var(--text-color);
  font-family: inherit;
  font-size: 1rem;
  transition: all 0.2s ease-in-out;
  box-shadow: var(--shadow-sm);
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  transform: translateY(-2px);
}

textarea {
  resize: vertical;
  min-height: 120px;
}

/* Buttons */
button, .button, input[type="submit"] {
  padding: 1rem 2rem;
  border-radius: var(--border-radius-md);
  border: none;
  background: var(--gradient-primary);
  color: var(--bg-color);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  font-size: 1rem;
  text-decoration: none;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

button::before, .button::before, input[type="submit"]::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s ease;
}

button:hover::before, .button:hover::before, input[type="submit"]:hover::before {
  left: 100%;
}

button:hover, .button:hover, input[type="submit"]:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-xl);
}

.button.small {
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
}

.button.secondary {
  background: var(--secondary-bg);
  color: var(--text-color);
  border: 2px solid var(--border-color);
}

.button.secondary:hover {
  background: var(--tertiary-bg);
  border-color: var(--primary-color);
}

.button.danger {
  background: linear-gradient(135deg, var(--error-color), #dc2626);
}

.button.danger:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
}

.button.success {
  background: linear-gradient(135deg, var(--success-color), #059669);
}

.button.success:hover {
  background: linear-gradient(135deg, #059669, #047857);
}

/* Utilities */
.page-title {
  margin-bottom: 3rem;
  font-size: 3rem;
  text-align: center;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
  letter-spacing: -0.02em;
}

.error-message {
  color: var(--error-color);
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  padding: 1rem;
  border-radius: var(--border-radius-md);
  margin-bottom: 1.5rem;
  font-weight: 500;
}

.success-message {
  color: var(--success-color);
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
  padding: 1rem;
  border-radius: var(--border-radius-md);
  margin-bottom: 1.5rem;
  font-weight: 500;
}

/* Animations */
.animated-icon {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

a:hover .animated-icon, button:hover .animated-icon {
  transform: scale(1.2) rotate(5deg);
}

/* Floating Animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

/* Pulse Animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.pulse-animation {
  animation: pulse 2s ease-in-out infinite;
}

/* Modal */
.image-modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(10px);
  overflow: auto;
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.image-modal.active {
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-content {
  max-width: 90%;
  max-height: 90%;
  object-fit: contain;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  animation: modalSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(-50px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.close-modal {
  position: absolute;
  top: 2rem;
  right: 2rem;
  color: var(--bg-color);
  font-size: 2rem;
  font-weight: bold;
  cursor: pointer;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-modal:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(1.1) rotate(90deg);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .container {
    padding: 0 1.5rem;
  }

  .page-title {
    font-size: 2.5rem;
  }

  nav {
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1.5rem;
    padding: 0 1rem;
  }

  nav {
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
  }

  nav a {
    padding: 0.75rem 1.25rem;
    font-size: 0.875rem;
  }

  .page-title {
    font-size: 2rem;
    margin-bottom: 2rem;
  }

  .card, .journal-detail, .entry-card, .auth-container {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .entry-image {
    height: 200px;
  }

  button, .button, input[type="submit"] {
    padding: 0.875rem 1.5rem;
    font-size: 0.875rem;
  }

  .entry-list {
    grid-template-columns: 1fr;
  }

  main {
    padding: 2rem 0;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 1rem;
  }

  .logo h1 {
    font-size: 1.5rem;
  }

  .page-title {
    font-size: 1.75rem;
  }

  .card, .journal-detail, .entry-card, .auth-container {
    padding: 1rem;
  }

  .entry-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  button, .button, input[type="submit"] {
    width: 100%;
    justify-content: center;
  }

  .social-links a {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1.25rem;
  }
}

/* Remove theme switcher */
.theme-switcher {
  display: none !important;
}

/* Additional Modern Elements */
.gradient-accent {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-primary);
  opacity: 0.03;
  z-index: -2;
  pointer-events: none;
}

.particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  pointer-events: none;
  overflow: hidden;
}

.particle {
  position: absolute;
  background: var(--primary-color);
  border-radius: 50%;
  opacity: 0.1;
  animation: particleFloat 15s infinite linear;
}

.particle:nth-child(1) { width: 4px; height: 4px; left: 10%; animation-delay: 0s; }
.particle:nth-child(2) { width: 6px; height: 6px; left: 20%; animation-delay: 2s; }
.particle:nth-child(3) { width: 3px; height: 3px; left: 30%; animation-delay: 4s; }
.particle:nth-child(4) { width: 5px; height: 5px; left: 40%; animation-delay: 6s; }
.particle:nth-child(5) { width: 4px; height: 4px; left: 50%; animation-delay: 8s; }
.particle:nth-child(6) { width: 7px; height: 7px; left: 60%; animation-delay: 10s; }
.particle:nth-child(7) { width: 3px; height: 3px; left: 70%; animation-delay: 12s; }

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.1;
  }
  90% {
    opacity: 0.1;
  }
  100% {
    transform: translateY(-100vh) rotate(360deg);
    opacity: 0;
  }
}




