{% extends 'app/base.html' %}

{% block content %}
<div class="templates-container">
    <div class="page-header">
        <h2 class="page-title"><i class="fas fa-file-alt animated-icon"></i> Journal Templates</h2>
        <p>Choose a template to start your journal entry with predefined structure</p>
    </div>
    
    <!-- Work Templates -->
    {% if template_categories.work %}
    <div class="templates-grid">
        <h3 class="section-title"><i class="fas fa-briefcase"></i> Work & Professional</h3>
        <div class="template-cards">
            {% for template in template_categories.work %}
            <div class="template-card" data-type="{{ template.template_type }}">
                <div class="template-icon">
                    <i class="{{ template.icon }} animated-icon"></i>
                </div>
                <div class="template-info">
                    <h3>{{ template.name }}</h3>
                    <p>{{ template.description }}</p>
                </div>
                <div class="template-actions">
                    <a href="{% url 'journal_new_with_template' template_id=template.id %}" class="button primary-btn">
                        <i class="fas fa-plus animated-icon"></i> Use Template
                    </a>
                    <button class="button small preview-btn" data-template-content="{{ template.content_structure|escapejs }}">
                        <i class="fas fa-eye"></i> Preview
                    </button>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    <!-- Personal Templates -->
    {% if template_categories.personal %}
    <div class="templates-grid">
        <h3 class="section-title"><i class="fas fa-user"></i> Personal Growth</h3>
        <div class="template-cards">
            {% for template in template_categories.personal %}
            <div class="template-card" data-type="{{ template.template_type }}">
                <div class="template-icon">
                    <i class="{{ template.icon }} animated-icon"></i>
                </div>
                <div class="template-info">
                    <h3>{{ template.name }}</h3>
                    <p>{{ template.description }}</p>
                </div>
                <div class="template-actions">
                    <a href="{% url 'journal_new_with_template' template_id=template.id %}" class="button primary-btn">
                        <i class="fas fa-plus animated-icon"></i> Use Template
                    </a>
                    <button class="button small preview-btn" data-template-content="{{ template.content_structure|escapejs }}">
                        <i class="fas fa-eye"></i> Preview
                    </button>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    <!-- Holiday Templates -->
    {% if template_categories.holiday %}
    <div class="templates-grid">
        <h3 class="section-title"><i class="fas fa-plane"></i> Holidays & Travel</h3>
        <div class="template-cards">
            {% for template in template_categories.holiday %}
            <div class="template-card" data-type="{{ template.template_type }}">
                <div class="template-icon">
                    <i class="{{ template.icon }} animated-icon"></i>
                </div>
                <div class="template-info">
                    <h3>{{ template.name }}</h3>
                    <p>{{ template.description }}</p>
                </div>
                <div class="template-actions">
                    <a href="{% url 'journal_new_with_template' template_id=template.id %}" class="button primary-btn">
                        <i class="fas fa-plus animated-icon"></i> Use Template
                    </a>
                    <button class="button small preview-btn" data-template-content="{{ template.content_structure|escapejs }}">
                        <i class="fas fa-eye"></i> Preview
                    </button>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    <!-- Other Templates -->
    {% if template_categories.other %}
    <div class="templates-grid">
        <h3 class="section-title"><i class="fas fa-th-large"></i> Other Templates</h3>
        <div class="template-cards">
            {% for template in template_categories.other %}
            <div class="template-card" data-type="{{ template.template_type }}">
                <div class="template-icon">
                    <i class="{{ template.icon }} animated-icon"></i>
                </div>
                <div class="template-info">
                    <h3>{{ template.name }}</h3>
                    <p>{{ template.description }}</p>
                </div>
                <div class="template-actions">
                    <a href="{% url 'journal_new_with_template' template_id=template.id %}" class="button primary-btn">
                        <i class="fas fa-plus animated-icon"></i> Use Template
                    </a>
                    <button class="button small preview-btn" data-template-content="{{ template.content_structure|escapejs }}">
                        <i class="fas fa-eye"></i> Preview
                    </button>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    <!-- Classic Templates -->
    <div class="templates-grid">
        <h3 class="section-title"><i class="fas fa-star"></i> Classic Templates</h3>
        <div class="template-cards">
            {% for template in template_categories.gratitude %}
            <div class="template-card" data-type="{{ template.template_type }}">
                <div class="template-icon">
                    <i class="{{ template.icon }} animated-icon"></i>
                </div>
                <div class="template-info">
                    <h3>{{ template.name }}</h3>
                    <p>{{ template.description }}</p>
                </div>
                <div class="template-actions">
                    <a href="{% url 'journal_new_with_template' template_id=template.id %}" class="button primary-btn">
                        <i class="fas fa-plus animated-icon"></i> Use Template
                    </a>
                    <button class="button small preview-btn" data-template-content="{{ template.content_structure|escapejs }}">
                        <i class="fas fa-eye"></i> Preview
                    </button>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    
    <h3 class="section-title">Your Custom Templates</h3>
    <div class="template-cards">
        {% for template in user_templates %}
        <div class="template-card">
            <div class="template-icon">
                <i class="{{ template.icon }} animated-icon"></i>
            </div>
            <div class="template-info">
                <h3>{{ template.name }}</h3>
                <p>{{ template.description }}</p>
                <div class="template-type-badge custom">Custom Template</div>
            </div>
            <div class="template-actions">
                <a href="{% url 'journal_new_with_template' template_id=template.id %}" class="button primary-btn">
                    <i class="fas fa-plus animated-icon"></i> Use Template
                </a>
                <button class="button small preview-btn" data-template-content="{{ template.content_structure|escapejs }}">
                    <i class="fas fa-eye animated-icon"></i> Preview
                </button>
                <a href="#" class="button small">
                    <i class="fas fa-edit animated-icon"></i> Edit
                </a>
                <a href="#" class="button small danger">
                    <i class="fas fa-trash animated-icon"></i> Delete
                </a>
            </div>
        </div>
        {% empty %}
        <div class="empty-state small">
            <div class="empty-icon">
                <i class="fas fa-plus-circle"></i>
            </div>
            <p>You haven't created any custom templates yet.</p>
            <p>Create your own templates to streamline your journaling process.</p>
            <a href="#" class="button primary-btn">
                <i class="fas fa-plus animated-icon"></i> Create Template
            </a>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Template Preview Modal -->
<div id="templatePreviewModal" class="modal">
    <div class="modal-content">
        <span class="close-modal">&times;</span>
        <h3>Template Preview</h3>
        <div class="template-preview-content">
            <pre id="templatePreviewText"></pre>
        </div>
    </div>
</div>

<script>
    // Template preview functionality
    document.addEventListener('DOMContentLoaded', function() {
        const modal = document.getElementById('templatePreviewModal');
        const previewText = document.getElementById('templatePreviewText');
        const previewBtns = document.querySelectorAll('.preview-btn');
        const closeBtn = document.querySelector('.close-modal');
        
        previewBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const templateContent = this.getAttribute('data-template-content');
                previewText.textContent = templateContent;
                modal.style.display = 'block';
            });
        });
        
        closeBtn.addEventListener('click', function() {
            modal.style.display = 'none';
        });
        
        window.addEventListener('click', function(event) {
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        });
    });
</script>

<style>
    /* Template type badge */
    .template-type-badge {
        display: inline-block;
        padding: 0.3rem 0.8rem;
        border-radius: 20px;
        font-size: 0.8rem;
        background: var(--accent-purple);
        color: white;
        margin-top: 0.8rem;
    }
    
    .template-type-badge.custom {
        background: var(--accent-teal);
    }
    
    /* Modal styles */
    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgba(0,0,0,0.7);
    }
    
    .modal-content {
        background-color: var(--card-bg);
        margin: 10% auto;
        padding: 2rem;
        border: 1px solid var(--border-color);
        border-radius: 12px;
        width: 80%;
        max-width: 700px;
        box-shadow: 0 5px 30px rgba(0,0,0,0.3);
        position: relative;
    }
    
    .close-modal {
        color: var(--text-color);
        position: absolute;
        top: 1rem;
        right: 1.5rem;
        font-size: 1.8rem;
        font-weight: bold;
        cursor: pointer;
    }
    
    .close-modal:hover {
        color: var(--accent-purple);
    }
    
    .template-preview-content {
        margin-top: 1.5rem;
        background: var(--dark-surface);
        padding: 1.5rem;
        border-radius: 8px;
        max-height: 400px;
        overflow-y: auto;
    }
    
    .template-preview-content pre {
        white-space: pre-wrap;
        font-family: 'Courier New', monospace;
        margin: 0;
        line-height: 1.5;
    }
    
    [data-theme="light"] .template-preview-content {
        background: #f5f5f5;
    }
</style>
{% endblock %}





