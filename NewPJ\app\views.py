from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth.forms import User<PERSON>reationForm, AuthenticationForm, PasswordChangeForm
from django.contrib.auth import login, logout, update_session_auth_hash
from django.contrib import messages
from django.utils import timezone
from django.http import JsonResponse
from datetime import timedelta, datetime, date
import json
import calendar
from .models import JournalEntry, JournalReminder, JournalStreak, JournalTemplate
from .forms import JournalEntryForm, JournalReminderForm, UserProfileForm

# Authentication views
def signup_view(request):
    if request.method == 'POST':
        form = UserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            login(request, user)
            messages.success(request, "Account created successfully!")
            return redirect('journal_list')
    else:
        form = UserCreationForm()
    return render(request, 'app/signup.html', {'form': form})

def login_view(request):
    if request.method == 'POST':
        form = AuthenticationForm(data=request.POST)
        if form.is_valid():
            user = form.get_user()
            login(request, user)

            # Handle "remember me" functionality
            if not request.POST.get('remember_me', None):
                request.session.set_expiry(0)

            # Redirect to next page if specified, otherwise to journal list
            next_page = request.GET.get('next', 'journal_list')
            return redirect(next_page)
    else:
        form = AuthenticationForm()
    return render(request, 'app/login.html', {'form': form})

def logout_view(request):
    logout(request)
    messages.info(request, "You have been logged out.")
    return redirect('login')

@login_required
def profile_view(request):
    if request.method == 'POST':
        form = UserProfileForm(request.POST, instance=request.user)
        password_form = PasswordChangeForm(request.user, request.POST)

        # Check which form was submitted
        if 'update_profile' in request.POST and form.is_valid():
            form.save()
            messages.success(request, "Your profile has been updated!")
            return redirect('profile')

        elif 'change_password' in request.POST and password_form.is_valid():
            user = password_form.save()
            # Update the session to prevent logging out
            update_session_auth_hash(request, user)
            messages.success(request, "Your password has been changed!")
            return redirect('profile')
    else:
        form = UserProfileForm(instance=request.user)
        password_form = PasswordChangeForm(request.user)

    # Get user stats
    entry_count = JournalEntry.objects.filter(user=request.user).count()
    streak = JournalStreak.objects.get_or_create(user=request.user)[0]

    return render(request, 'app/profile.html', {
        'form': form,
        'password_form': password_form,
        'entry_count': entry_count,
        'streak': streak
    })

# Journal views
@login_required
def journal_list(request):
    entries = JournalEntry.objects.filter(user=request.user)
    return render(request, 'app/journal_list.html', {'entries': entries})

@login_required
def journal_detail(request, pk):
    entry = get_object_or_404(JournalEntry, pk=pk, user=request.user)
    return render(request, 'app/journal_detail.html', {'entry': entry})

@login_required
def journal_new(request):
    templates = JournalTemplate.objects.filter(is_default=True)

    # Handle date parameter from calendar
    selected_date = request.GET.get('date')
    initial_data = {}

    if selected_date:
        try:
            # Parse the date and create a title suggestion
            date_obj = datetime.strptime(selected_date, '%Y-%m-%d').date()
            initial_data['title'] = f"Journal Entry - {date_obj.strftime('%B %d, %Y')}"
        except ValueError:
            pass

    if request.method == 'POST':
        form = JournalEntryForm(request.POST, request.FILES)
        template_id = request.POST.get('template_id')

        if form.is_valid():
            entry = form.save(commit=False)
            entry.user = request.user

            # If creating entry for a specific date, set the created_at
            if selected_date:
                try:
                    date_obj = datetime.strptime(selected_date, '%Y-%m-%d')
                    # Set to the selected date but keep current time
                    current_time = timezone.now().time()
                    entry.created_at = timezone.make_aware(
                        datetime.combine(date_obj.date(), current_time)
                    )
                except ValueError:
                    pass

            entry.save()

            # Update streak
            today = timezone.now().date()
            streak, created = JournalStreak.objects.get_or_create(user=request.user)

            # If this is the first entry or it's been more than a day since the last entry
            if created or (today - streak.last_entry_date).days > 1:
                streak.current_streak = 1
            # If the entry is from today, don't increment the streak
            elif (today - streak.last_entry_date).days == 0:
                pass
            # If the entry is from yesterday, increment the streak
            elif (today - streak.last_entry_date).days == 1:
                streak.current_streak += 1

            # Update longest streak if needed
            if streak.current_streak > streak.longest_streak:
                streak.longest_streak = streak.current_streak

            streak.last_entry_date = today
            streak.save()

            messages.success(request, 'Journal entry created successfully!')
            return redirect('journal_detail', pk=entry.pk)
    else:
        form = JournalEntryForm(initial=initial_data)

    return render(request, 'app/journal_edit.html', {
        'form': form,
        'templates': templates,
        'selected_date': selected_date
    })

@login_required
def journal_edit(request, pk):
    entry = get_object_or_404(JournalEntry, pk=pk, user=request.user)
    if request.method == "POST":
        form = JournalEntryForm(request.POST, request.FILES, instance=entry)
        if form.is_valid():
            entry = form.save(commit=False)
            entry.updated_at = timezone.now()
            entry.save()
            return redirect('journal_detail', pk=entry.pk)
    else:
        form = JournalEntryForm(instance=entry)
    return render(request, 'app/journal_edit.html', {'form': form})

@login_required
def journal_delete(request, pk):
    entry = get_object_or_404(JournalEntry, pk=pk, user=request.user)
    entry.delete()
    return redirect('journal_list')

@login_required
def journal_search(request):
    query = request.GET.get('q', '')
    tag = request.GET.get('tag', '')
    category = request.GET.get('category', '')

    entries = JournalEntry.objects.filter(user=request.user)

    if query:
        entries = entries.filter(title__icontains=query) | entries.filter(content__icontains=query)
    if tag:
        entries = entries.filter(tags__icontains=tag)
    if category:
        entries = entries.filter(category=category)

    return render(request, 'app/journal_list.html', {
        'entries': entries,
        'query': query
    })

@login_required
def reminder_list(request):
    reminders = JournalReminder.objects.filter(user=request.user)
    streak = JournalStreak.objects.get_or_create(user=request.user)[0]

    return render(request, 'app/reminder_list.html', {
        'reminders': reminders,
        'streak': streak
    })

@login_required
def reminder_create(request):
    if request.method == 'POST':
        form = JournalReminderForm(request.POST)
        if form.is_valid():
            reminder = form.save(commit=False)
            reminder.user = request.user
            reminder.save()
            messages.success(request, 'Reminder created successfully!')
            return redirect('reminder_list')
    else:
        form = JournalReminderForm()

    return render(request, 'app/reminder_form.html', {'form': form})

@login_required
def reminder_edit(request, pk):
    reminder = get_object_or_404(JournalReminder, pk=pk, user=request.user)

    if request.method == 'POST':
        form = JournalReminderForm(request.POST, instance=reminder)
        if form.is_valid():
            form.save()
            messages.success(request, 'Reminder updated successfully!')
            return redirect('reminder_list')
    else:
        form = JournalReminderForm(instance=reminder)

    return render(request, 'app/reminder_form.html', {'form': form})

@login_required
def reminder_delete(request, pk):
    reminder = get_object_or_404(JournalReminder, pk=pk, user=request.user)

    if request.method == 'POST':
        reminder.delete()
        messages.success(request, 'Reminder deleted successfully!')
        return redirect('reminder_list')

    return render(request, 'app/reminder_confirm_delete.html', {'reminder': reminder})

@login_required
def reminder_toggle(request, pk):
    reminder = get_object_or_404(JournalReminder, pk=pk, user=request.user)

    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            reminder.active = data.get('active', False)
            reminder.save()
            return JsonResponse({'success': True})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Invalid request method'})

@login_required
def journal_new_with_template(request, template_id):
    template = get_object_or_404(JournalTemplate, pk=template_id)

    # Check if user can access this template (is it default or created by this user)
    if not template.is_default and template.created_by != request.user:
        messages.error(request, "You don't have permission to use this template.")
        return redirect('template_list')

    # Set initial data for the form
    initial_data = {
        'content': template.content_structure,
        'title': f"{template.name} - {timezone.now().strftime('%B %d, %Y')}"
    }

    if request.method == "POST":
        form = JournalEntryForm(request.POST, request.FILES)
        if form.is_valid():
            entry = form.save(commit=False)
            entry.user = request.user
            entry.save()

            # Update streak
            today = timezone.now().date()
            streak, created = JournalStreak.objects.get_or_create(user=request.user)

            # If this is the first entry or it's been more than a day since the last entry
            if created or not streak.last_entry_date:
                streak.current_streak = 1
            elif streak.last_entry_date == today:
                # Already journaled today, streak doesn't change
                pass
            elif streak.last_entry_date == today - timedelta(days=1):
                # Consecutive day, increase streak
                streak.current_streak += 1
            else:
                # Streak broken
                streak.current_streak = 1

            # Update longest streak if needed
            if streak.current_streak > streak.longest_streak:
                streak.longest_streak = streak.current_streak

            streak.last_entry_date = today
            streak.save()

            messages.success(request, 'Journal entry created successfully!')
            return redirect('journal_detail', pk=entry.pk)
    else:
        form = JournalEntryForm(initial=initial_data)

    return render(request, 'app/journal_edit.html', {
        'form': form,
        'template': template
    })

@login_required
def template_list(request):
    # Get all default templates
    default_templates = JournalTemplate.objects.filter(is_default=True)

    # Get user's custom templates
    custom_templates = JournalTemplate.objects.filter(created_by=request.user, is_default=False)

    # Organize templates by type
    template_categories = {
        'work': default_templates.filter(template_type='work'),
        'personal': default_templates.filter(template_type='personal'),
        'holiday': default_templates.filter(template_type='holiday'),
        'gratitude': default_templates.filter(template_type='gratitude'),
        'reflection': default_templates.filter(template_type='reflection'),
        'dream': default_templates.filter(template_type='dream'),
        'goal': default_templates.filter(template_type='goal'),
        'travel': default_templates.filter(template_type='travel'),
        'food': default_templates.filter(template_type='food'),
        'other': default_templates.filter(template_type='other'),
    }

    return render(request, 'app/template_list.html', {
        'template_categories': template_categories,
        'custom_templates': custom_templates,
    })

# Calendar views
@login_required
def calendar_view(request, year=None, month=None):
    """Display calendar view with journal entries"""
    today = timezone.now().date()

    if year is None:
        year = today.year
    if month is None:
        month = today.month

    # Create calendar
    cal = calendar.monthcalendar(year, month)

    # Get entries for this month
    entries = JournalEntry.objects.filter(
        user=request.user,
        created_at__year=year,
        created_at__month=month
    ).order_by('created_at')

    # Group entries by day
    entries_by_day = {}
    for entry in entries:
        day = entry.created_at.day
        if day not in entries_by_day:
            entries_by_day[day] = []
        entries_by_day[day].append(entry)

    # Navigation dates
    if month == 1:
        prev_month, prev_year = 12, year - 1
    else:
        prev_month, prev_year = month - 1, year

    if month == 12:
        next_month, next_year = 1, year + 1
    else:
        next_month, next_year = month + 1, year

    context = {
        'calendar': cal,
        'year': year,
        'month': month,
        'month_name': calendar.month_name[month],
        'entries_by_day': entries_by_day,
        'today': today,
        'prev_month': prev_month,
        'prev_year': prev_year,
        'next_month': next_month,
        'next_year': next_year,
    }

    return render(request, 'app/calendar_view.html', context)

@login_required
def calendar_day_view(request, year, month, day):
    """Display detailed view for a specific day"""
    try:
        selected_date = date(year, month, day)
    except ValueError:
        messages.error(request, "Invalid date selected.")
        return redirect('calendar_view')

    # Get entries for this specific day
    entries = JournalEntry.objects.filter(
        user=request.user,
        created_at__date=selected_date
    ).order_by('created_at')

    context = {
        'selected_date': selected_date,
        'entries': entries,
        'year': year,
        'month': month,
        'day': day,
    }

    return render(request, 'app/calendar_day_view.html', context)
