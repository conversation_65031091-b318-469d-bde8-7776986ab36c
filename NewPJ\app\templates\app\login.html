{% extends 'app/base.html' %}

{% block content %}
<!-- Link Auth CSS -->
<link rel="stylesheet" href="/static/css/auth.css">

<div class="auth-container">
    <div class="auth-header">
        <div class="auth-icon">
            <i class="fas fa-sign-in-alt"></i>
        </div>
        <h1>Welcome Back</h1>
        <p>Sign in to your account to continue your journey</p>
    </div>

    <form method="post" class="auth-form">
        {% csrf_token %}

        <div class="auth-form-group with-icon">
            <label for="id_username">Username</label>
            {{ form.username }}
            <i class="fas fa-user"></i>
        </div>

        <div class="auth-form-group with-icon">
            <label for="id_password">Password</label>
            {{ form.password }}
            <i class="fas fa-lock"></i>
        </div>

        <div class="auth-checkbox">
            <input type="checkbox" name="remember_me" id="id_remember_me">
            <label for="id_remember_me">Remember me for 30 days</label>
        </div>

        {% if form.errors %}
            <div class="auth-error">
                <i class="fas fa-exclamation-triangle"></i>
                <span>Your username and password didn't match. Please try again.</span>
            </div>
        {% endif %}

        <button type="submit" class="auth-btn">
            <i class="fas fa-sign-in-alt"></i>
            <span>Sign In</span>
            <div class="btn-glow"></div>
        </button>

        <button type="button" class="auth-btn auth-btn-secondary" onclick="demoLogin()">
            <i class="fas fa-eye"></i>
            <span>Demo Login</span>
        </button>
    </form>

    <div class="auth-links">
        <a href="{% url 'password_reset' %}">
            <i class="fas fa-key"></i>
            Forgot your password?
        </a>
        <p>Don't have an account?
            <a href="{% url 'signup' %}">
                <i class="fas fa-user-plus"></i>
                Create one now
            </a>
        </p>
    </div>
</div>

<script>
function demoLogin() {
    document.getElementById('id_username').value = 'demo';
    document.getElementById('id_password').value = 'demo123';

    // Add visual feedback
    const btn = event.target.closest('button');
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>Loading Demo...</span>';

    setTimeout(() => {
        document.querySelector('form').submit();
    }, 1000);
}

// Add input focus animations
document.addEventListener('DOMContentLoaded', function() {
    const inputs = document.querySelectorAll('.auth-form-group input');

    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        input.addEventListener('blur', function() {
            if (!this.value) {
                this.parentElement.classList.remove('focused');
            }
        });

        // Check if input has value on load
        if (input.value) {
            input.parentElement.classList.add('focused');
        }
    });
});
</script>

<style>
.auth-form-group.focused label {
    transform: translateY(-25px) scale(0.8);
    color: var(--primary-color);
}

.auth-form-group.with-icon.focused i {
    color: var(--primary-color);
    transform: translateY(-50%) scale(1.1);
}

.btn-glow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: translateX(-100%);
    transition: transform 0.6s;
}

.auth-btn:hover .btn-glow {
    transform: translateX(100%);
}
</style>
{% endblock %}
