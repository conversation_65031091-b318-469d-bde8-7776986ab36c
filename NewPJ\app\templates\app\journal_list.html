{% extends 'app/base.html' %}

{% block content %}
<!-- Link Journal List CSS -->
<link rel="stylesheet" href="/static/css/journal-list.css">

<div class="journal-header">
    <h1><i class="fas fa-book-open"></i> My Journal Collection</h1>
    <p>Your personal space for thoughts, memories, and inspirations</p>
</div>

<div class="journal-actions">
    <div class="journal-search">
        <i class="fas fa-search"></i>
        <form method="get" action="{% url 'journal_search' %}">
            <input type="text" name="q" placeholder="Search your journals..." value="{{ query|default:'' }}" id="searchInput">
        </form>
    </div>

    <div class="journal-filter">
        <button class="filter-btn active" data-filter="all">
            <i class="fas fa-list"></i> All
        </button>
        <button class="filter-btn" data-filter="recent">
            <i class="fas fa-clock"></i> Recent
        </button>
        <button class="filter-btn" data-filter="favorites">
            <i class="fas fa-heart"></i> Favorites
        </button>
    </div>

    <a href="{% url 'journal_new' %}" class="journal-create-btn">
        <i class="fas fa-plus"></i>
        <span>New Entry</span>
        <div class="btn-ripple"></div>
    </a>
</div>

<!-- Quick Stats -->
<div class="journal-stats">
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-book"></i>
        </div>
        <div class="stat-content">
            <div class="stat-number">{{ entries.count }}</div>
            <div class="stat-label">Total Entries</div>
        </div>
    </div>

    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-calendar-week"></i>
        </div>
        <div class="stat-content">
            <div class="stat-number">{{ this_week_count|default:0 }}</div>
            <div class="stat-label">This Week</div>
        </div>
    </div>

    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-fire"></i>
        </div>
        <div class="stat-content">
            <div class="stat-number">{{ streak_days|default:0 }}</div>
            <div class="stat-label">Day Streak</div>
        </div>
    </div>

    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-chart-line"></i>
        </div>
        <div class="stat-content">
            <div class="stat-number">{{ avg_per_week|default:0 }}</div>
            <div class="stat-label">Avg/Week</div>
        </div>
    </div>
</div>

<div class="journal-grid">
    {% for entry in entries %}
    <div class="journal-card" data-category="{{ entry.category|default:'general' }}" data-date="{{ entry.created_at|date:'Y-m-d' }}">
        <div class="journal-card-header">
            <div class="journal-icon">
                <i class="fas fa-book-open"></i>
            </div>
            <div class="journal-card-title">
                <h3>{{ entry.title }}</h3>
                <div class="journal-card-date">
                    <i class="far fa-calendar-alt"></i>
                    {{ entry.created_at|date:"M j, Y" }}
                </div>
            </div>
            <div class="journal-favorite" onclick="toggleFavorite({{ entry.pk }})">
                <i class="far fa-heart"></i>
            </div>
        </div>

        {% if entry.image %}
        <div class="journal-image" onclick="openImageModal('{{ entry.image.url }}')">
            <img src="{{ entry.image.url }}" alt="{{ entry.title }}">
            <div class="image-overlay">
                <i class="fas fa-expand"></i>
            </div>
        </div>
        {% endif %}

        <div class="journal-card-content">
            <div class="journal-card-description">
                {{ entry.content|truncatewords:25 }}
            </div>

            {% if entry.tags %}
            <div class="journal-tags">
                {% for tag in entry.tags.split|slice:":3" %}
                <span class="journal-tag">
                    <i class="fas fa-tag"></i>
                    {{ tag }}
                </span>
                {% endfor %}
            </div>
            {% endif %}

            <div class="journal-card-stats">
                <div class="journal-stat">
                    <i class="fas fa-eye"></i>
                    <span>{{ entry.views|default:0 }}</span>
                </div>
                <div class="journal-stat">
                    <i class="fas fa-clock"></i>
                    <span>{{ entry.read_time|default:5 }} min</span>
                </div>
                <div class="journal-stat">
                    <i class="fas fa-heart"></i>
                    <span>{{ entry.likes|default:0 }}</span>
                </div>
            </div>
        </div>

        <div class="journal-card-actions">
            <a href="{% url 'journal_detail' pk=entry.pk %}" class="journal-action-btn primary">
                <i class="fas fa-book-reader"></i>
                Read More
            </a>
            <a href="{% url 'journal_edit' pk=entry.pk %}" class="journal-action-btn">
                <i class="fas fa-edit"></i>
                Edit
            </a>
            <button class="journal-action-btn" onclick="shareEntry({{ entry.pk }})">
                <i class="fas fa-share"></i>
                Share
            </button>
        </div>
    </div>
    {% empty %}
    <div class="journal-empty">
        <div class="journal-empty-icon">
            <i class="fas fa-book-open"></i>
        </div>
        <h3>No Journal Entries Yet</h3>
        <p>Start your journaling journey by creating your first entry. Capture your thoughts, memories, and experiences.</p>
        <a href="{% url 'journal_new' %}" class="journal-create-btn">
            <i class="fas fa-plus"></i>
            <span>Create Your First Entry</span>
        </a>
    </div>
    {% endfor %}
</div>

<script>
// Enhanced JavaScript functionality
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchInput = document.getElementById('searchInput');
    const journalCards = document.querySelectorAll('.journal-card');

    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();

            journalCards.forEach(card => {
                const title = card.querySelector('h3').textContent.toLowerCase();
                const content = card.querySelector('.journal-card-description').textContent.toLowerCase();

                if (title.includes(searchTerm) || content.includes(searchTerm)) {
                    card.style.display = 'block';
                    card.style.animation = 'fadeIn 0.3s ease';
                } else {
                    card.style.display = 'none';
                }
            });
        });
    }

    // Filter functionality
    const filterBtns = document.querySelectorAll('.filter-btn');

    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            // Remove active class from all buttons
            filterBtns.forEach(b => b.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');

            const filter = this.dataset.filter;

            journalCards.forEach(card => {
                if (filter === 'all') {
                    card.style.display = 'block';
                } else if (filter === 'recent') {
                    const cardDate = new Date(card.dataset.date);
                    const weekAgo = new Date();
                    weekAgo.setDate(weekAgo.getDate() - 7);

                    if (cardDate >= weekAgo) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                } else if (filter === 'favorites') {
                    // This would need backend support
                    card.style.display = 'block';
                }
            });
        });
    });
});

function toggleFavorite(entryId) {
    const heartIcon = event.target;

    // Toggle heart icon
    if (heartIcon.classList.contains('far')) {
        heartIcon.classList.remove('far');
        heartIcon.classList.add('fas');
        heartIcon.style.color = '#ef4444';
    } else {
        heartIcon.classList.remove('fas');
        heartIcon.classList.add('far');
        heartIcon.style.color = '';
    }

    // Here you would make an AJAX call to update the backend
    console.log('Toggled favorite for entry:', entryId);
}

function shareEntry(entryId) {
    if (navigator.share) {
        navigator.share({
            title: 'Check out my journal entry',
            url: window.location.origin + '/journal/' + entryId + '/'
        });
    } else {
        // Fallback - copy to clipboard
        const url = window.location.origin + '/journal/' + entryId + '/';
        navigator.clipboard.writeText(url).then(() => {
            alert('Link copied to clipboard!');
        });
    }
}

function openImageModal(imageSrc) {
    // Create modal if it doesn't exist
    let modal = document.getElementById('imageModal');
    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'imageModal';
        modal.className = 'image-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <span class="close-modal" onclick="closeImageModal()">&times;</span>
                <img id="modalImage" src="" alt="">
            </div>
        `;
        document.body.appendChild(modal);
    }

    const modalImg = document.getElementById('modalImage');
    modal.style.display = 'flex';
    modalImg.src = imageSrc;

    // Add fade in animation
    setTimeout(() => {
        modal.classList.add('active');
    }, 10);
}

function closeImageModal() {
    const modal = document.getElementById('imageModal');
    if (modal) {
        modal.classList.remove('active');

        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }
}
</script>

<style>
/* Additional styles for enhanced features */
.journal-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-4);
    margin-bottom: var(--space-8);
}

.stat-card {
    background: var(--bg-surface);
    border-radius: var(--radius-xl);
    padding: var(--space-4);
    border: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    gap: var(--space-3);
    transition: all var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    background: var(--gradient-accent);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-primary);
    font-size: var(--font-size-lg);
}

.stat-number {
    font-size: var(--font-size-2xl);
    font-weight: 800;
    color: var(--text-primary);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.journal-favorite {
    cursor: pointer;
    padding: var(--space-2);
    border-radius: var(--radius-full);
    transition: all var(--transition-normal);
}

.journal-favorite:hover {
    background: var(--bg-elevated);
    transform: scale(1.1);
}

.journal-image {
    position: relative;
    height: 200px;
    overflow: hidden;
    border-radius: var(--radius-lg);
    margin: var(--space-4) 0;
    cursor: pointer;
}

.journal-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.journal-image:hover .image-overlay {
    opacity: 1;
}

.journal-image:hover img {
    transform: scale(1.05);
}

.image-overlay i {
    color: var(--text-primary);
    font-size: var(--font-size-2xl);
}

.journal-tags {
    display: flex;
    gap: var(--space-2);
    flex-wrap: wrap;
    margin: var(--space-3) 0;
}

.journal-tag {
    background: var(--bg-elevated);
    color: var(--text-secondary);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

/* Image Modal Styles */
.image-modal {
    display: none;
    position: fixed;
    z-index: var(--z-modal);
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.image-modal.active {
    opacity: 1;
}

.image-modal .modal-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
}

.image-modal img {
    width: 100%;
    height: auto;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-2xl);
}

.close-modal {
    position: absolute;
    top: -3rem;
    right: 0;
    color: var(--text-primary);
    font-size: var(--font-size-2xl);
    cursor: pointer;
    background: var(--bg-surface);
    width: 3rem;
    height: 3rem;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-normal);
}

.close-modal:hover {
    background: var(--error-color);
    transform: scale(1.1);
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}
</style>
{% endblock %}




