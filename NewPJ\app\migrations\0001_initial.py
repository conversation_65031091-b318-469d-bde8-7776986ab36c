# Generated by Django 3.1.12

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='JournalEntry',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('content', models.TextField()),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('image', models.ImageField(blank=True, null=True, upload_to='journal_images/')),
                ('category', models.CharField(blank=True, max_length=100)),
                ('tags', models.Char<PERSON><PERSON>(blank=True, help_text='Comma-separated tags', max_length=200)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='journal_entries', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'Journal Entries',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='JournalTemplate',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('template_type', models.CharField(choices=[('gratitude', 'Gratitude Journal'), ('reflection', 'Daily Reflection'), ('dream', 'Dream Log'), ('goal', 'Goal Setting'), ('travel', 'Travel Journal'), ('food', 'Food Journal'), ('custom', 'Custom Template')], max_length=20)),
                ('content_structure', models.TextField(help_text='Template structure with placeholders')),
                ('icon', models.CharField(default='fas fa-book', max_length=50)),
                ('is_default', models.BooleanField(default=False)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='JournalStreak',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('current_streak', models.IntegerField(default=0)),
                ('longest_streak', models.IntegerField(default=0)),
                ('last_entry_date', models.DateField(blank=True, null=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='journal_streak', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='JournalReminder',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100)),
                ('frequency', models.CharField(choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('custom', 'Custom')], default='daily', max_length=20)),
                ('time', models.TimeField()),
                ('days', models.CharField(blank=True, help_text='Comma-separated days (for weekly/custom)', max_length=100)),
                ('active', models.BooleanField(default=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='journal_reminders', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
