{% extends 'app/base.html' %}
{% load custom_filters %}

{% block content %}
<link rel="stylesheet" href="/static/css/calendar.css">

<div class="calendar-container">
<div class="calendar-wrapper">
    <div class="calendar-header">
        <div class="calendar-navigation">
            <a href="{% url 'calendar_month' prev_year prev_month %}" class="nav-btn">
                <i class="fas fa-chevron-left"></i>
            </a>
            <h2 class="calendar-title">
                <i class="fas fa-calendar-alt animated-icon"></i>
                {{ month_name }} {{ year }}
            </h2>
            <a href="{% url 'calendar_month' next_year next_month %}" class="nav-btn">
                <i class="fas fa-chevron-right"></i>
            </a>
        </div>

        <div class="calendar-actions">
            <a href="{% url 'journal_new' %}" class="button primary-btn">
                <i class="fas fa-plus animated-icon"></i> New Entry
            </a>
            <a href="{% url 'calendar_view' %}" class="button secondary">
                <i class="fas fa-home animated-icon"></i> Today
            </a>
        </div>
    </div>

    <div class="calendar-grid">
        <!-- Day headers -->
        <div class="calendar-weekdays">
            <div class="weekday">Sun</div>
            <div class="weekday">Mon</div>
            <div class="weekday">Tue</div>
            <div class="weekday">Wed</div>
            <div class="weekday">Thu</div>
            <div class="weekday">Fri</div>
            <div class="weekday">Sat</div>
        </div>

        <!-- Calendar days -->
        <div class="calendar-days">
            {% for week in calendar %}
                {% for day in week %}
                    {% if day == 0 %}
                        <div class="calendar-day empty"></div>
                    {% else %}
                        <div class="calendar-day {% if day == today.day and month == today.month and year == today.year %}today{% endif %} {% if day in entries_by_day %}has-entries{% endif %}"
                             data-day="{{ day }}" data-month="{{ month }}" data-year="{{ year }}">
                            <div class="day-number">{{ day }}</div>

                            {% if day in entries_by_day %}
                                <div class="day-entries">
                                    {% for entry in entries_by_day|lookup:day %}
                                        <div class="entry-indicator" title="{{ entry.title }}">
                                            <i class="fas fa-circle"></i>
                                        </div>
                                    {% endfor %}
                                    {% if entries_by_day|lookup:day|length > 3 %}
                                        <div class="more-entries">+{{ entries_by_day|lookup:day|length|add:"-3" }}</div>
                                    {% endif %}
                                </div>
                            {% endif %}

                            <div class="day-actions">
                                <a href="{% url 'calendar_day' year month day %}" class="view-day" title="View day details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'journal_new' %}?date={{ year }}-{{ month|stringformat:'02d' }}-{{ day|stringformat:'02d' }}"
                                   class="add-entry" title="Add entry for this day">
                                    <i class="fas fa-plus"></i>
                                </a>
                            </div>
                        </div>
                    {% endif %}
                {% endfor %}
            {% endfor %}
        </div>
    </div>

    <!-- Calendar Legend -->
    <div class="calendar-legend">
        <div class="legend-item">
            <div class="legend-color today-color"></div>
            <span>Today</span>
        </div>
        <div class="legend-item">
            <div class="legend-color has-entries-color"></div>
            <span>Has Entries</span>
        </div>
        <div class="legend-item">
            <i class="fas fa-circle entry-indicator-sample"></i>
            <span>Journal Entry</span>
        </div>
    </div>

    <!-- Quick Entry Preview Modal -->
    <div id="entryPreviewModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="previewDate"></h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body" id="previewContent">
                <!-- Entry previews will be loaded here -->
            </div>
        </div>
    </div>
</div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('entryPreviewModal');
    const calendarDays = document.querySelectorAll('.calendar-day[data-day]');
    const closeBtn = document.querySelector('.close-modal');

    // Add click handlers for calendar days
    calendarDays.forEach(day => {
        day.addEventListener('click', function(e) {
            // Don't trigger if clicking on action buttons
            if (e.target.closest('.day-actions')) {
                return;
            }

            const dayNum = this.dataset.day;
            const month = this.dataset.month;
            const year = this.dataset.year;

            // Redirect to day view
            window.location.href = `/calendar/day/${year}/${month}/${dayNum}/`;
        });
    });

    // Close modal
    if (closeBtn) {
        closeBtn.addEventListener('click', function() {
            modal.style.display = 'none';
        });
    }

    // Close modal when clicking outside
    window.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });
});
</script>

{% endblock %}
