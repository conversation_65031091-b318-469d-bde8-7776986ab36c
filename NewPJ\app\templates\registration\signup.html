{% extends 'app/base.html' %}

{% block content %}
<div class="auth-container">
    <div class="logo">
        <div class="svg-logo-container">
            <svg viewBox="0 0 512 512" class="news-logo">
                <!-- Green notebook with tabs -->
                <path d="M50 60 C50 40 70 20 90 20 L422 20 C442 20 462 40 462 60 L462 452 C462 472 442 492 422 492 L90 492 C70 492 50 472 50 452 Z" fill="#4CAF8C" />
                <path d="M90 20 L422 20 C442 20 462 40 462 60 L462 452 C462 472 442 492 422 492 L256 492 L256 20 Z" fill="#3D9D7C" />
                
                <!-- Notebook rings/binding -->
                <circle cx="50" cy="100" r="20" fill="#333333" />
                <circle cx="50" cy="200" r="20" fill="#333333" />
                <circle cx="50" cy="300" r="20" fill="#333333" />
                <circle cx="50" cy="400" r="20" fill="#333333" />
                
                <!-- Colored tabs -->
                <rect x="442" y="100" width="40" height="50" rx="10" ry="10" fill="#FFD54F" />
                <rect x="442" y="220" width="40" height="50" rx="10" ry="10" fill="#FF5252" />
                
                <!-- Note area -->
                <rect x="150" y="80" width="180" height="90" rx="10" ry="10" fill="#E0E0E0" />
                <rect x="170" y="110" width="140" height="10" rx="5" ry="5" fill="#9E9E9E" />
                <rect x="170" y="140" width="140" height="10" rx="5" ry="5" fill="#9E9E9E" />
            </svg>
        </div>
    </div>
    <h2>Create Your Journal Account</h2>
    
    <form method="post">
        {% csrf_token %}
        <div class="form-group">
            <label for="id_username">Username</label>
            {{ form.username }}
            {% if form.username.errors %}
            <div class="error-message">{{ form.username.errors }}</div>
            {% endif %}
        </div>
        
        <div class="form-group">
            <label for="id_email">Email</label>
            {{ form.email }}
            {% if form.email.errors %}
            <div class="error-message">{{ form.email.errors }}</div>
            {% endif %}
        </div>
        
        <div class="form-group">
            <label for="id_password1">Password</label>
            {{ form.password1 }}
            {% if form.password1.errors %}
            <div class="error-message">{{ form.password1.errors }}</div>
            {% endif %}
        </div>
        
        <div class="form-group">
            <label for="id_password2">Confirm Password</label>
            {{ form.password2 }}
            {% if form.password2.errors %}
            <div class="error-message">{{ form.password2.errors }}</div>
            {% endif %}
        </div>
        
        <button type="submit">Sign Up</button>
    </form>
    
    <div class="auth-link">
        <p>Already have an account? <a href="{% url 'login' %}">Log In</a></p>
    </div>
</div>
{% endblock %}



