{% extends 'app/base.html' %}

{% block content %}
<div class="auth-container">
    <h2>Sign Up</h2>
    <form method="post">
        {% csrf_token %}
        <div class="form-group">
            <label for="id_username">Username:</label>
            {{ form.username }}
            {% if form.username.errors %}
                <div class="error-message">{{ form.username.errors }}</div>
            {% endif %}
        </div>
        <div class="form-group">
            <label for="id_password1">Password:</label>
            {{ form.password1 }}
            {% if form.password1.errors %}
                <div class="error-message">{{ form.password1.errors }}</div>
            {% endif %}
        </div>
        <div class="form-group">
            <label for="id_password2">Confirm Password:</label>
            {{ form.password2 }}
            {% if form.password2.errors %}
                <div class="error-message">{{ form.password2.errors }}</div>
            {% endif %}
        </div>
        <button type="submit">Sign Up</button>
    </form>
    <p class="auth-link">Already have an account? <a href="{% url 'login' %}">Log in</a></p>
</div>
{% endblock %}