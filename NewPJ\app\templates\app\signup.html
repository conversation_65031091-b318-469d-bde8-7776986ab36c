{% extends 'app/base.html' %}

{% block content %}
<!-- Link Auth CSS -->
<link rel="stylesheet" href="/static/css/auth.css">

<div class="auth-container">
    <div class="auth-header">
        <div class="auth-icon">
            <i class="fas fa-user-plus"></i>
        </div>
        <h1>Join Us Today</h1>
        <p>Create your account and start your journaling journey</p>
    </div>

    <form method="post" class="auth-form">
        {% csrf_token %}

        <div class="auth-form-group with-icon">
            <label for="id_username">Username</label>
            {{ form.username }}
            <i class="fas fa-user"></i>
            {% if form.username.errors %}
                <div class="auth-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>{{ form.username.errors.0 }}</span>
                </div>
            {% endif %}
        </div>

        <div class="auth-form-group with-icon">
            <label for="id_password1">Password</label>
            {{ form.password1 }}
            <i class="fas fa-lock"></i>
            {% if form.password1.errors %}
                <div class="auth-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>{{ form.password1.errors.0 }}</span>
                </div>
            {% endif %}
        </div>

        <div class="auth-form-group with-icon">
            <label for="id_password2">Confirm Password</label>
            {{ form.password2 }}
            <i class="fas fa-shield-alt"></i>
            {% if form.password2.errors %}
                <div class="auth-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>{{ form.password2.errors.0 }}</span>
                </div>
            {% endif %}
        </div>

        <!-- Password Strength Indicator -->
        <div class="password-strength">
            <div class="strength-bar">
                <div class="strength-fill" id="strengthFill"></div>
            </div>
            <div class="strength-text" id="strengthText">Password strength</div>
        </div>

        <div class="auth-checkbox">
            <input type="checkbox" id="terms" required>
            <label for="terms">I agree to the <a href="#" target="_blank">Terms of Service</a> and <a href="#" target="_blank">Privacy Policy</a></label>
        </div>

        <button type="submit" class="auth-btn" id="signupBtn">
            <i class="fas fa-user-plus"></i>
            <span>Create Account</span>
            <div class="btn-glow"></div>
        </button>

        <div class="auth-divider">
            <span>or</span>
        </div>

        <button type="button" class="auth-btn auth-btn-secondary" onclick="quickSignup()">
            <i class="fas fa-magic"></i>
            <span>Quick Signup</span>
        </button>
    </form>

    <div class="auth-links">
        <p>Already have an account?
            <a href="{% url 'login' %}">
                <i class="fas fa-sign-in-alt"></i>
                Sign in here
            </a>
        </p>
    </div>
</div>

<script>
// Password strength checker
function checkPasswordStrength(password) {
    let strength = 0;
    const checks = {
        length: password.length >= 8,
        lowercase: /[a-z]/.test(password),
        uppercase: /[A-Z]/.test(password),
        numbers: /\d/.test(password),
        special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
    };

    strength = Object.values(checks).filter(Boolean).length;

    const strengthFill = document.getElementById('strengthFill');
    const strengthText = document.getElementById('strengthText');

    const levels = [
        { text: 'Very Weak', color: '#ef4444', width: '20%' },
        { text: 'Weak', color: '#f59e0b', width: '40%' },
        { text: 'Fair', color: '#eab308', width: '60%' },
        { text: 'Good', color: '#22c55e', width: '80%' },
        { text: 'Strong', color: '#10b981', width: '100%' }
    ];

    const level = levels[strength - 1] || levels[0];

    strengthFill.style.width = level.width;
    strengthFill.style.background = level.color;
    strengthText.textContent = level.text;
    strengthText.style.color = level.color;
}

function quickSignup() {
    const username = 'user_' + Math.random().toString(36).substr(2, 9);
    const password = 'TempPass123!';

    document.getElementById('id_username').value = username;
    document.getElementById('id_password1').value = password;
    document.getElementById('id_password2').value = password;
    document.getElementById('terms').checked = true;

    // Visual feedback
    const btn = event.target.closest('button');
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>Creating Account...</span>';

    setTimeout(() => {
        document.querySelector('form').submit();
    }, 1500);
}

document.addEventListener('DOMContentLoaded', function() {
    const password1 = document.getElementById('id_password1');
    const password2 = document.getElementById('id_password2');
    const signupBtn = document.getElementById('signupBtn');

    // Password strength checking
    password1.addEventListener('input', function() {
        checkPasswordStrength(this.value);
    });

    // Password match checking
    function checkPasswordMatch() {
        if (password2.value && password1.value !== password2.value) {
            password2.style.borderColor = '#ef4444';
            password2.parentElement.classList.add('error');
        } else {
            password2.style.borderColor = '';
            password2.parentElement.classList.remove('error');
        }
    }

    password2.addEventListener('input', checkPasswordMatch);

    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        if (password1.value !== password2.value) {
            e.preventDefault();
            alert('Passwords do not match!');
        }
    });
});
</script>

<style>
.password-strength {
    margin: var(--space-4) 0;
}

.strength-bar {
    width: 100%;
    height: 4px;
    background: var(--bg-elevated);
    border-radius: var(--radius-full);
    overflow: hidden;
    margin-bottom: var(--space-2);
}

.strength-fill {
    height: 100%;
    width: 0%;
    transition: all 0.3s ease;
    border-radius: var(--radius-full);
}

.strength-text {
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-align: center;
}

.auth-divider {
    text-align: center;
    margin: var(--space-4) 0;
    position: relative;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--border-primary);
}

.auth-divider span {
    background: var(--bg-surface);
    padding: 0 var(--space-3);
    color: var(--text-muted);
    font-size: var(--font-size-sm);
}

.auth-form-group.error input {
    border-color: #ef4444 !important;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2) !important;
}
</style>
{% endblock %}