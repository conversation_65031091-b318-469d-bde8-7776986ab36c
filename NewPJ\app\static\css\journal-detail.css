/* Journal Detail Page Styles */

/* Journal Detail Container */
.journal-detail-container {
  max-width: 900px;
  margin: 0 auto;
  padding: var(--space-6);
}

/* Journal Header */
.journal-detail-header {
  background: var(--bg-surface);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  margin-bottom: var(--space-8);
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-xl);
  position: relative;
  overflow: hidden;
}

.journal-detail-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
  box-shadow: var(--glow-primary);
}

.journal-detail-header::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-mesh);
  opacity: 0.05;
  z-index: -1;
}

.journal-title-section {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.journal-detail-icon {
  width: 4rem;
  height: 4rem;
  background: var(--gradient-primary);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
  font-size: var(--font-size-2xl);
  box-shadow: var(--shadow-lg), var(--glow-primary);
  animation: iconPulse 3s ease-in-out infinite;
}

@keyframes iconPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.journal-title-content h1 {
  font-size: var(--font-size-4xl);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--space-2);
  font-weight: 800;
}

.journal-meta {
  display: flex;
  gap: var(--space-6);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.journal-meta-item {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.journal-meta-item i {
  color: var(--primary-color);
}

.journal-description {
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
  font-size: var(--font-size-lg);
  margin-bottom: var(--space-6);
}

/* Journal Actions */
.journal-detail-actions {
  display: flex;
  gap: var(--space-3);
  flex-wrap: wrap;
}

.journal-action-btn {
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  font-weight: 600;
  text-decoration: none;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--font-size-sm);
  cursor: pointer;
  border: none;
}

.journal-action-btn.primary {
  background: var(--gradient-primary);
  color: var(--text-primary);
  box-shadow: var(--shadow-md), var(--glow-primary);
}

.journal-action-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl), var(--glow-primary);
}

.journal-action-btn.secondary {
  background: var(--bg-elevated);
  color: var(--text-secondary);
  border: 1px solid var(--border-primary);
}

.journal-action-btn.secondary:hover {
  background: var(--bg-overlay);
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.journal-action-btn.danger {
  background: var(--gradient-error);
  color: var(--text-primary);
  box-shadow: var(--shadow-md), var(--glow-error);
}

.journal-action-btn.danger:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl), var(--glow-error);
}

/* Entries Section */
.journal-entries-section {
  background: var(--bg-surface);
  border-radius: var(--radius-2xl);
  padding: var(--space-6);
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-lg);
}

.entries-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-4);
  border-bottom: 1px solid var(--border-primary);
}

.entries-header h2 {
  font-size: var(--font-size-2xl);
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.entries-header i {
  color: var(--accent-color);
}

.entries-count {
  background: var(--gradient-accent);
  color: var(--text-primary);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: 600;
  box-shadow: var(--shadow-sm), var(--glow-accent);
}

/* Entry Card */
.entry-card {
  background: var(--bg-elevated);
  border-radius: var(--radius-xl);
  padding: var(--space-5);
  margin-bottom: var(--space-4);
  border: 1px solid var(--border-primary);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.entry-card::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--gradient-accent);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.entry-card:hover::before {
  opacity: 1;
}

.entry-card:hover {
  transform: translateX(8px);
  box-shadow: var(--shadow-lg);
}

.entry-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-3);
}

.entry-title {
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  font-weight: 600;
  margin: 0;
}

.entry-date {
  color: var(--text-muted);
  font-size: var(--font-size-sm);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.entry-content {
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
  margin-bottom: var(--space-3);
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.entry-actions {
  display: flex;
  gap: var(--space-2);
}

.entry-action-btn {
  padding: var(--space-1) var(--space-3);
  background: var(--bg-surface);
  color: var(--text-muted);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  text-decoration: none;
  font-size: var(--font-size-xs);
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.entry-action-btn:hover {
  color: var(--primary-color);
  border-color: var(--primary-color);
  background: var(--bg-overlay);
}

/* Empty Entries State */
.entries-empty {
  text-align: center;
  padding: var(--space-8);
  color: var(--text-muted);
}

.entries-empty-icon {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--space-4);
  opacity: 0.5;
}

.entries-empty h3 {
  font-size: var(--font-size-xl);
  color: var(--text-secondary);
  margin-bottom: var(--space-2);
}

.entries-empty p {
  margin-bottom: var(--space-4);
}

/* Responsive Design */
@media (max-width: 768px) {
  .journal-detail-container {
    padding: var(--space-4);
  }
  
  .journal-detail-header {
    padding: var(--space-6);
  }
  
  .journal-title-section {
    flex-direction: column;
    text-align: center;
    gap: var(--space-3);
  }
  
  .journal-meta {
    justify-content: center;
    flex-wrap: wrap;
    gap: var(--space-4);
  }
  
  .journal-detail-actions {
    justify-content: center;
  }
  
  .entries-header {
    flex-direction: column;
    gap: var(--space-3);
    text-align: center;
  }
}

@media (max-width: 480px) {
  .journal-detail-header {
    padding: var(--space-4);
  }
  
  .journal-title-content h1 {
    font-size: var(--font-size-3xl);
  }
  
  .journal-detail-actions {
    flex-direction: column;
  }
  
  .journal-action-btn {
    justify-content: center;
  }
  
  .entry-header {
    flex-direction: column;
    gap: var(--space-2);
  }
  
  .entry-actions {
    justify-content: center;
  }
}
