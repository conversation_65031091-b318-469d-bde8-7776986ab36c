<!DOCTYPE html>
<html>
<head>
    <title>Personal Journal</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Futuristic Dark Theme - No White Colors -->
    <style>
        /* Force dark theme variables */
        :root {
            --override-primary: #6366f1;
            --override-secondary: #8b5cf6;
            --override-accent: #06b6d4;
            --override-bg-primary: #0a0e1a;
            --override-bg-secondary: #1a1f2e;
            --override-bg-surface: #1e2532;
            --override-text-primary: #f1f5f9;
            --override-text-secondary: #cbd5e1;
            --override-border: #374151;
        }

        /* Global dark theme enforcement */
        * {
            scrollbar-width: thin;
            scrollbar-color: var(--override-primary) var(--override-bg-secondary);
        }

        *::-webkit-scrollbar {
            width: 8px;
        }

        *::-webkit-scrollbar-track {
            background: var(--override-bg-secondary);
        }

        *::-webkit-scrollbar-thumb {
            background: var(--override-primary);
            border-radius: 4px;
        }

        body {
            background: linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 50%, #2d3748 100%) !important;
            color: var(--override-text-primary) !important;
        }

        header {
            background: rgba(26, 31, 46, 0.95) !important;
            border-bottom: 1px solid var(--override-border) !important;
            backdrop-filter: blur(20px) !important;
        }

        footer {
            background: var(--override-bg-surface) !important;
            border-top: 1px solid var(--override-border) !important;
        }

        .card, .journal-detail, .entry-card, .auth-container {
            background: var(--override-bg-surface) !important;
            border: 1px solid var(--override-border) !important;
            color: var(--override-text-primary) !important;
        }

        input, textarea, select {
            background: var(--override-bg-secondary) !important;
            color: var(--override-text-primary) !important;
            border: 2px solid var(--override-border) !important;
        }

        input:focus, textarea:focus, select:focus {
            border-color: var(--override-primary) !important;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2) !important;
        }

        button, .button, input[type="submit"] {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #a855f7 100%) !important;
            color: var(--override-text-primary) !important;
        }

        a {
            color: var(--override-primary) !important;
        }

        a:hover {
            color: #818cf8 !important;
        }

        /* Typography overrides */
        .logo h1, h2, h3, h4, h5, h6 {
            color: var(--override-text-primary) !important;
        }

        nav a {
            color: var(--override-text-primary) !important;
        }

        nav a:hover {
            color: #818cf8 !important;
        }

        .entry-date, .text-secondary {
            color: var(--override-text-secondary) !important;
        }

        p {
            color: var(--override-text-secondary) !important;
        }

        /* Remove any white backgrounds */
        .bg-white, .bg-light {
            background: var(--override-bg-surface) !important;
        }

        /* Ensure no white text */
        .text-white {
            color: var(--override-text-primary) !important;
        }
    </style>
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="icon" href="/static/images/favicon.ico">
</head>
<body>
    <!-- Add floating particles -->
    <div class="particles">
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
    </div>

    <!-- Add professional gradient accents -->
    <div class="gradient-accent" style="background: linear-gradient(135deg, #6a11cb, #2575fc);"></div>

    <header>
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <svg viewBox="0 0 512 512" width="40" height="40" class="notebook-logo">
                        <!-- Green notebook with tabs -->
                        <path d="M50 60 C50 40 70 20 90 20 L422 20 C442 20 462 40 462 60 L462 452 C462 472 442 492 422 492 L90 492 C70 492 50 472 50 452 Z" fill="#4CAF8C" />
                        <path d="M90 20 L422 20 C442 20 462 40 462 60 L462 452 C462 472 442 492 422 492 L256 492 L256 20 Z" fill="#3D9D7C" />

                        <!-- Notebook rings/binding -->
                        <circle cx="50" cy="100" r="20" fill="#333333" />
                        <circle cx="50" cy="200" r="20" fill="#333333" />
                        <circle cx="50" cy="300" r="20" fill="#333333" />
                        <circle cx="50" cy="400" r="20" fill="#333333" />

                        <!-- Colored tabs -->
                        <rect x="442" y="100" width="40" height="50" rx="10" ry="10" fill="#FFD54F" />
                        <rect x="442" y="220" width="40" height="50" rx="10" ry="10" fill="#FF5252" />

                        <!-- Note area -->
                        <rect x="150" y="80" width="180" height="90" rx="10" ry="10" fill="#E0E0E0" />
                        <rect x="170" y="110" width="140" height="10" rx="5" ry="5" fill="#9E9E9E" />
                        <rect x="170" y="140" width="140" height="10" rx="5" ry="5" fill="#9E9E9E" />
                    </svg>
                    <h1>Personal Journal</h1>
                </div>
                <nav>
                    {% if user.is_authenticated %}
                    <a href="{% url 'journal_list' %}" class="nav-link"><i class="fas fa-book animated-icon"></i> My Journal</a>
                    <a href="{% url 'calendar_view' %}" class="nav-link"><i class="fas fa-calendar-alt animated-icon"></i> Calendar</a>
                    <a href="{% url 'template_list' %}" class="nav-link"><i class="fas fa-file-alt animated-icon"></i> Templates</a>
                    <a href="{% url 'reminder_list' %}" class="nav-link"><i class="fas fa-bell animated-icon"></i> Reminders</a>
                    <a href="{% url 'logout' %}" class="nav-link"><i class="fas fa-sign-out-alt animated-icon"></i> Logout</a>
                    {% else %}
                    <a href="{% url 'login' %}" class="nav-link"><i class="fas fa-sign-in-alt animated-icon"></i> Login</a>
                    <a href="{% url 'signup' %}" class="nav-link"><i class="fas fa-user-plus animated-icon"></i> Sign Up</a>
                    {% endif %}

                    <!-- Enhanced Theme Switcher -->
                    <div class="theme-switcher">
                        <button class="theme-toggle" id="lightTheme" title="Light Theme">
                            <i class="fas fa-sun"></i>
                        </button>
                        <button class="theme-toggle active" id="darkTheme" title="Dark Theme">
                            <i class="fas fa-moon"></i>
                        </button>
                    </div>
                </nav>
            </div>
        </div>
    </header>

    <main class="container">
        {% block content %}
        {% endblock %}
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>Personal Journal</h4>
                    <p>Capture your thoughts, memories, and inspirations in a beautiful, secure digital space.</p>
                    <div class="footer-links">
                        <a href="#about">About</a>
                        <a href="#privacy">Privacy Policy</a>
                        <a href="#terms">Terms of Service</a>
                    </div>
                </div>

                <div class="footer-section">
                    <h4>Features</h4>
                    <div class="footer-links">
                        <a href="{% url 'journal_list' %}">My Journals</a>
                        <a href="{% url 'calendar_view' %}">Calendar View</a>
                        <a href="#templates">Templates</a>
                        <a href="#reminders">Reminders</a>
                    </div>
                </div>

                <div class="footer-section">
                    <h4>Connect With Us</h4>
                    <div class="social-links">
                        <a href="#" class="social-link" title="Facebook"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-link" title="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-link" title="Instagram"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="social-link" title="LinkedIn"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" class="social-link" title="GitHub"><i class="fab fa-github"></i></a>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2025 Personal Journal App. All rights reserved.</p>
                <div class="footer-links">
                    <a href="#help">Help Center</a>
                    <a href="#contact">Contact</a>
                    <a href="#support">Support</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button class="back-to-top" id="backToTop" title="Back to Top">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Image Modal for Expanded View -->
    <div class="image-modal" id="imageModal">
        <span class="close-modal" onclick="closeModal()">&times;</span>
        <img class="modal-content" id="expandedImg">
    </div>

    <!-- Add theme switcher button -->
    <div class="theme-switcher" id="themeSwitcher">
        <i class="fas fa-sun" id="themeIcon"></i>
    </div>

    <script>
        // Image modal functionality
        function openModal(imgSrc) {
            const modal = document.getElementById('imageModal');
            const modalImg = document.getElementById('expandedImg');

            modalImg.src = imgSrc;
            modal.classList.add('active');

            // Prevent scrolling when modal is open
            document.body.style.overflow = 'hidden';
        }

        function closeModal() {
            const modal = document.getElementById('imageModal');
            modal.classList.remove('active');

            // Re-enable scrolling
            document.body.style.overflow = 'auto';
        }

        // Close modal when clicking outside the image
        document.getElementById('imageModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // Enhanced Theme Switcher functionality
        document.addEventListener('DOMContentLoaded', function() {
            const lightTheme = document.getElementById('lightTheme');
            const darkTheme = document.getElementById('darkTheme');

            // Set dark theme as default
            const savedTheme = localStorage.getItem('theme') || 'dark';
            document.documentElement.setAttribute('data-theme', savedTheme);
            updateThemeDisplay(savedTheme);

            // Light theme button
            lightTheme.addEventListener('click', function() {
                setTheme('light');
            });

            // Dark theme button
            darkTheme.addEventListener('click', function() {
                setTheme('dark');
            });

            function setTheme(theme) {
                document.documentElement.setAttribute('data-theme', theme);
                localStorage.setItem('theme', theme);
                updateThemeDisplay(theme);

                // Add animation effect
                document.body.style.transition = 'all 0.3s ease';
                setTimeout(() => {
                    document.body.style.transition = '';
                }, 300);
            }

            function updateThemeDisplay(theme) {
                lightTheme.classList.toggle('active', theme === 'light');
                darkTheme.classList.toggle('active', theme === 'dark');
            }
        });

        // Back to Top Button functionality
        document.addEventListener('DOMContentLoaded', function() {
            const backToTop = document.getElementById('backToTop');

            // Show/hide button based on scroll position
            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    backToTop.classList.add('visible');
                } else {
                    backToTop.classList.remove('visible');
                }
            });

            // Smooth scroll to top
            backToTop.addEventListener('click', function() {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        });
    </script>
    <!-- Create animated background particles -->
    <div class="particles-container" id="particles"></div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 15; // Number of particles

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';

                // Random size between 3px and 8px
                const size = Math.random() * 5 + 3;
                particle.style.width = `${size}px`;
                particle.style.height = `${size}px`;

                // Random position
                particle.style.left = `${Math.random() * 100}%`;
                particle.style.top = `${Math.random() * 100}%`;

                // Random animation duration between 15s and 30s
                const duration = Math.random() * 15 + 15;
                particle.style.animationDuration = `${duration}s`;

                // Random animation delay
                particle.style.animationDelay = `${Math.random() * 5}s`;

                particlesContainer.appendChild(particle);
            }
        });
    </script>
    <!-- Set dark theme immediately and permanently -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Force dark theme
            document.documentElement.setAttribute('data-theme', 'dark');
            localStorage.setItem('theme', 'dark');

            // Update theme icon
            const themeIcon = document.getElementById('themeIcon');
            const themeText = document.getElementById('themeText');

            if(themeIcon) {
                themeIcon.className = 'fas fa-moon animated-icon';
            }

            if(themeText) {
                themeText.textContent = 'Dark Mode';
            }

            // Disable theme switching
            const themeSwitcher = document.getElementById('themeSwitcher');
            if(themeSwitcher) {
                themeSwitcher.removeEventListener('click', null);
            }
        });
    </script>
</body>
</html>



