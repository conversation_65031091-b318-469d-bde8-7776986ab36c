<!DOCTYPE html>
<html>
<head>
    <title>Personal Journal</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Add inline styles to force dark theme with your color palette -->
    <style>
        /* Dark Theme - Using your exact color palette */
        :root {
            --primary-bg: #4a1a4a;      /* Deep purple */
            --secondary-bg: #6b5b4a;    /* Olive brown */
            --text-color: #f5f5dc;      /* Cream */
            --accent-color: #ff6b47;    /* Orange */
        }

        body {
            background-color: var(--primary-bg) !important;
            color: var(--text-color) !important;
        }

        header, footer {
            background-color: var(--primary-bg) !important;
            border-bottom: 1px solid var(--secondary-bg) !important;
        }

        .card, .journal-detail, .entry-card, .auth-container {
            background-color: var(--secondary-bg) !important;
            border: 1px solid var(--secondary-bg) !important;
            color: var(--text-color) !important;
        }

        input, textarea, select {
            background-color: var(--primary-bg) !important;
            color: var(--text-color) !important;
            border: 1px solid var(--secondary-bg) !important;
        }

        button, .button, input[type="submit"] {
            background-color: var(--accent-color) !important;
            color: var(--text-color) !important;
        }

        a {
            color: var(--accent-color) !important;
        }

        a:hover {
            color: var(--text-color) !important;
        }

        .theme-switcher {
            display: none !important;
        }

        /* Additional elements */
        .logo h1, h2, h3, h4, h5, h6 {
            color: var(--text-color) !important;
        }

        nav a {
            color: var(--text-color) !important;
        }

        nav a:hover {
            color: var(--accent-color) !important;
        }

        .entry-date, .text-secondary {
            color: var(--text-color) !important;
            opacity: 0.8;
        }
    </style>
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="icon" href="/static/images/favicon.ico">
</head>
<body>
    <!-- Add floating particles -->
    <div class="particles">
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
    </div>

    <!-- Add professional gradient accents -->
    <div class="gradient-accent" style="background: linear-gradient(135deg, #6a11cb, #2575fc);"></div>

    <header>
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <svg viewBox="0 0 512 512" width="40" height="40" class="notebook-logo">
                        <!-- Green notebook with tabs -->
                        <path d="M50 60 C50 40 70 20 90 20 L422 20 C442 20 462 40 462 60 L462 452 C462 472 442 492 422 492 L90 492 C70 492 50 472 50 452 Z" fill="#4CAF8C" />
                        <path d="M90 20 L422 20 C442 20 462 40 462 60 L462 452 C462 472 442 492 422 492 L256 492 L256 20 Z" fill="#3D9D7C" />

                        <!-- Notebook rings/binding -->
                        <circle cx="50" cy="100" r="20" fill="#333333" />
                        <circle cx="50" cy="200" r="20" fill="#333333" />
                        <circle cx="50" cy="300" r="20" fill="#333333" />
                        <circle cx="50" cy="400" r="20" fill="#333333" />

                        <!-- Colored tabs -->
                        <rect x="442" y="100" width="40" height="50" rx="10" ry="10" fill="#FFD54F" />
                        <rect x="442" y="220" width="40" height="50" rx="10" ry="10" fill="#FF5252" />

                        <!-- Note area -->
                        <rect x="150" y="80" width="180" height="90" rx="10" ry="10" fill="#E0E0E0" />
                        <rect x="170" y="110" width="140" height="10" rx="5" ry="5" fill="#9E9E9E" />
                        <rect x="170" y="140" width="140" height="10" rx="5" ry="5" fill="#9E9E9E" />
                    </svg>
                    <h1>Personal Journal</h1>
                </div>
                <nav>
                    {% if user.is_authenticated %}
                    <a href="{% url 'journal_list' %}"><i class="fas fa-book animated-icon"></i> My Journal</a>
                    <a href="{% url 'calendar_view' %}"><i class="fas fa-calendar-alt animated-icon"></i> Calendar</a>
                    <a href="{% url 'template_list' %}"><i class="fas fa-file-alt animated-icon"></i> Templates</a>
                    <a href="{% url 'reminder_list' %}"><i class="fas fa-bell animated-icon"></i> Reminders</a>
                    <a href="{% url 'logout' %}"><i class="fas fa-sign-out-alt animated-icon"></i> Logout</a>
                    {% else %}
                    <a href="{% url 'login' %}"><i class="fas fa-sign-in-alt animated-icon"></i> Login</a>
                    <a href="{% url 'signup' %}"><i class="fas fa-user-plus animated-icon"></i> Sign Up</a>
                    {% endif %}
                </nav>
            </div>
        </div>
    </header>

    <main class="container">
        {% block content %}
        {% endblock %}
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <p>&copy; 2025 Personal Journal App</p>
                <div class="social-links">
                    <a href="#"><i class="fab fa-facebook-f animated-icon"></i></a>
                    <a href="#"><i class="fab fa-twitter animated-icon"></i></a>
                    <a href="#"><i class="fab fa-instagram animated-icon"></i></a>
                    <a href="#"><i class="fab fa-linkedin-in animated-icon"></i></a>
                </div>
                <p>Capture your thoughts, memories, and inspirations</p>
            </div>
        </div>
    </footer>

    <!-- Image Modal for Expanded View -->
    <div class="image-modal" id="imageModal">
        <span class="close-modal" onclick="closeModal()">&times;</span>
        <img class="modal-content" id="expandedImg">
    </div>

    <!-- Add theme switcher button -->
    <div class="theme-switcher" id="themeSwitcher">
        <i class="fas fa-sun" id="themeIcon"></i>
    </div>

    <script>
        // Image modal functionality
        function openModal(imgSrc) {
            const modal = document.getElementById('imageModal');
            const modalImg = document.getElementById('expandedImg');

            modalImg.src = imgSrc;
            modal.classList.add('active');

            // Prevent scrolling when modal is open
            document.body.style.overflow = 'hidden';
        }

        function closeModal() {
            const modal = document.getElementById('imageModal');
            modal.classList.remove('active');

            // Re-enable scrolling
            document.body.style.overflow = 'auto';
        }

        // Close modal when clicking outside the image
        document.getElementById('imageModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // Theme switcher functionality
        document.addEventListener('DOMContentLoaded', function() {
            const themeSwitcher = document.getElementById('themeSwitcher');
            const themeIcon = document.getElementById('themeIcon');
            const themeText = document.getElementById('themeText');

            // Check for saved theme preference or use dark as default
            const savedTheme = localStorage.getItem('theme') || 'dark';
            document.documentElement.setAttribute('data-theme', savedTheme);

            // Update icon and text based on current theme
            updateThemeDisplay(savedTheme);

            // Toggle theme on click
            themeSwitcher.addEventListener('click', function(e) {
                e.preventDefault(); // Prevent the link from navigating

                let theme = document.documentElement.getAttribute('data-theme');
                let newTheme = theme === 'dark' ? 'light' : 'dark';

                document.documentElement.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);

                updateThemeDisplay(newTheme);
            });

            function updateThemeDisplay(theme) {
                if (theme === 'dark') {
                    themeIcon.className = 'fas fa-sun animated-icon';
                    themeText.textContent = 'Light Mode';
                } else {
                    themeIcon.className = 'fas fa-moon animated-icon';
                    themeText.textContent = 'Dark Mode';
                }
            }
        });
    </script>
    <!-- Create animated background particles -->
    <div class="particles-container" id="particles"></div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 15; // Number of particles

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';

                // Random size between 3px and 8px
                const size = Math.random() * 5 + 3;
                particle.style.width = `${size}px`;
                particle.style.height = `${size}px`;

                // Random position
                particle.style.left = `${Math.random() * 100}%`;
                particle.style.top = `${Math.random() * 100}%`;

                // Random animation duration between 15s and 30s
                const duration = Math.random() * 15 + 15;
                particle.style.animationDuration = `${duration}s`;

                // Random animation delay
                particle.style.animationDelay = `${Math.random() * 5}s`;

                particlesContainer.appendChild(particle);
            }
        });
    </script>
    <!-- Set dark theme immediately and permanently -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Force dark theme
            document.documentElement.setAttribute('data-theme', 'dark');
            localStorage.setItem('theme', 'dark');

            // Update theme icon
            const themeIcon = document.getElementById('themeIcon');
            const themeText = document.getElementById('themeText');

            if(themeIcon) {
                themeIcon.className = 'fas fa-moon animated-icon';
            }

            if(themeText) {
                themeText.textContent = 'Dark Mode';
            }

            // Disable theme switching
            const themeSwitcher = document.getElementById('themeSwitcher');
            if(themeSwitcher) {
                themeSwitcher.removeEventListener('click', null);
            }
        });
    </script>
</body>
</html>



