{% extends 'app/base.html' %}

{% block content %}
<div class="form-container">
    <h2>{% if form.instance.pk %}Edit Journal Entry{% else %}New Journal Entry{% endif %}</h2>
    
    {% if not form.instance.pk %}
    <div class="template-selector">
        <label for="template-select">Choose a Template (Optional):</label>
        <select id="template-select" class="form-control">
            <option value="">-- No Template --</option>
            <optgroup label="Work & Professional">
                {% for template in templates %}
                    {% if template.template_type == 'work' %}
                    <option value="{{ template.id }}" data-content="{{ template.content_structure|escapejs }}">{{ template.name }}</option>
                    {% endif %}
                {% endfor %}
            </optgroup>
            <optgroup label="Personal Growth">
                {% for template in templates %}
                    {% if template.template_type == 'personal' %}
                    <option value="{{ template.id }}" data-content="{{ template.content_structure|escapejs }}">{{ template.name }}</option>
                    {% endif %}
                {% endfor %}
            </optgroup>
            <optgroup label="Holidays & Travel">
                {% for template in templates %}
                    {% if template.template_type == 'holiday' %}
                    <option value="{{ template.id }}" data-content="{{ template.content_structure|escapejs }}">{{ template.name }}</option>
                    {% endif %}
                {% endfor %}
            </optgroup>
            <optgroup label="Classic Templates">
                {% for template in templates %}
                    {% if template.template_type == 'gratitude' or template.template_type == 'reflection' %}
                    <option value="{{ template.id }}" data-content="{{ template.content_structure|escapejs }}">{{ template.name }}</option>
                    {% endif %}
                {% endfor %}
            </optgroup>
            <optgroup label="Other Templates">
                {% for template in templates %}
                    {% if template.template_type == 'other' %}
                    <option value="{{ template.id }}" data-content="{{ template.content_structure|escapejs }}">{{ template.name }}</option>
                    {% endif %}
                {% endfor %}
            </optgroup>
        </select>
    </div>
    {% endif %}
    
    <form method="post" enctype="multipart/form-data" id="journalForm">
        {% csrf_token %}
        <input type="hidden" id="template_id" name="template_id" value="">
        <div class="form-group">
            <label for="id_title">Title:</label>
            {{ form.title }}
            {% if form.title.errors %}
                <div class="error-message">{{ form.title.errors }}</div>
            {% endif %}
        </div>
        <div class="form-group">
            <label for="id_content">Content:</label>
            <div class="voice-input-container">
                {{ form.content }}
                <button type="button" id="voiceInputBtn" class="voice-btn" title="Start voice input">
                    <i class="fas fa-microphone"></i>
                </button>
                <div id="voiceStatus" class="voice-status">Click microphone to start speaking</div>
            </div>
            {% if form.content.errors %}
                <div class="error-message">{{ form.content.errors }}</div>
            {% endif %}
        </div>
        <div class="form-group">
            <label for="id_category">Category:</label>
            {{ form.category }}
            {% if form.category.errors %}
                <div class="error-message">{{ form.category.errors }}</div>
            {% endif %}
        </div>
        <div class="form-group">
            <label for="id_tags">Tags:</label>
            {{ form.tags }}
            {% if form.tags.errors %}
                <div class="error-message">{{ form.tags.errors }}</div>
            {% endif %}
        </div>
        <div class="form-group">
            <label for="id_image">Image:</label>
            {{ form.image }}
            {% if form.image.errors %}
                <div class="error-message">{{ form.image.errors }}</div>
            {% endif %}
        </div>
        <div class="form-group">
            <label for="id_audio">Audio:</label>
            {{ form.audio }}
            {% if form.audio.errors %}
                <div class="error-message">{{ form.audio.errors }}</div>
            {% endif %}
        </div>
        <div class="form-actions">
            <button type="submit" class="primary-btn">
                <i class="fas fa-save animated-icon"></i> Save Entry
            </button>
            <a href="#" id="cancelBtn" class="button secondary">
                <i class="fas fa-times-circle animated-icon"></i> Cancel
            </a>
        </div>
    </form>
</div>

<!-- Confirmation Dialog -->
<div class="confirm-dialog" id="cancelConfirmDialog">
    <div class="confirm-dialog-content">
        <h3><i class="fas fa-exclamation-triangle" style="color: #ff4b2b; margin-right: 10px;"></i> Discard Changes?</h3>
        <p>You have unsaved changes that will be lost if you leave this page. Are you sure you want to discard your entry?</p>
        <div class="confirm-dialog-actions">
            <a href="{% url 'journal_list' %}" class="button danger">
                <i class="fas fa-trash-alt animated-icon"></i> Discard
            </a>
            <button type="button" class="button secondary" id="continueEditingBtn">
                <i class="fas fa-edit animated-icon"></i> Keep Editing
            </button>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Template selection functionality
        const templateSelect = document.getElementById('template-select');
        const contentTextarea = document.getElementById('id_content');
        const templateIdInput = document.getElementById('template_id');
        
        if (templateSelect) {
            templateSelect.addEventListener('change', function() {
                const selectedOption = this.options[this.selectedIndex];
                const templateContent = selectedOption.getAttribute('data-content');
                const templateId = selectedOption.value;
                
                if (templateContent) {
                    contentTextarea.value = templateContent;
                }
                
                if (templateId) {
                    templateIdInput.value = templateId;
                } else {
                    templateIdInput.value = '';
                }
            });
        }
        
        // Voice input code (unchanged)
        const voiceInputBtn = document.getElementById('voiceInputBtn');
        const voiceStatus = document.getElementById('voiceStatus');
        
        // Form change tracking and cancel confirmation
        const form = document.getElementById('journalForm');
        const cancelBtn = document.getElementById('cancelBtn');
        const cancelDialog = document.getElementById('cancelConfirmDialog');
        const continueEditingBtn = document.getElementById('continueEditingBtn');
        
        let formChanged = false;
        
        // Track form changes
        const formInputs = form.querySelectorAll('input, textarea, select');
        formInputs.forEach(input => {
            input.addEventListener('change', () => {
                formChanged = true;
            });
            input.addEventListener('keyup', () => {
                formChanged = true;
            });
        });
        
        // Cancel button click handler
        cancelBtn.addEventListener('click', function(e) {
            e.preventDefault();
            
            if (formChanged) {
                // Show confirmation dialog
                cancelDialog.classList.add('active');
            } else {
                // No changes, redirect immediately
                window.location.href = "{% url 'journal_list' %}";
            }
        });
        
        // Continue editing button
        if (continueEditingBtn) {
            continueEditingBtn.addEventListener('click', function() {
                cancelDialog.classList.remove('active');
            });
        }
        
        // Close dialog when clicking outside
        if (cancelDialog) {
            cancelDialog.addEventListener('click', function(e) {
                if (e.target === this) {
                    cancelDialog.classList.remove('active');
                }
            });
        }
    });
</script>

<style>
    .template-selector {
        margin-bottom: 1.5rem;
        padding: 1rem;
        background: var(--card-bg);
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .template-selector label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: bold;
    }
    
    .template-selector select {
        width: 100%;
        padding: 0.75rem;
        border-radius: 4px;
        border: 1px solid var(--border-color);
        background: var(--input-bg);
        color: var(--text-color);
        font-size: 1rem;
    }
    
    .template-selector select:focus {
        outline: none;
        border-color: var(--accent-purple);
        box-shadow: 0 0 0 2px rgba(106, 17, 203, 0.2);
    }
</style>
{% endblock %}






