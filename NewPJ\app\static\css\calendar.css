/* Modern Calendar Design */
:root {
    --cal-primary: #6366f1;
    --cal-secondary: #8b5cf6;
    --cal-accent: #06b6d4;
    --cal-success: #10b981;
    --cal-warning: #f59e0b;
    --cal-danger: #ef4444;
    --cal-light: #f8fafc;
    --cal-dark: #1e293b;
    --cal-gray: #64748b;
    --cal-border: #e2e8f0;
    --cal-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --cal-shadow-lg: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Calendar Container */
.calendar-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 2rem 1rem;
    position: relative;
    overflow: hidden;
}

.calendar-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    pointer-events: none;
}

.calendar-wrapper {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    box-shadow: var(--cal-shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    z-index: 1;
}

/* Calendar Header */
.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, var(--cal-primary) 0%, var(--cal-secondary) 100%);
    border-radius: 20px;
    color: white;
    box-shadow: var(--cal-shadow);
    position: relative;
    overflow: hidden;
}

.calendar-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.calendar-navigation {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    z-index: 2;
    position: relative;
}

.nav-btn {
    background: rgba(255,255,255,0.15);
    color: white;
    border: 2px solid rgba(255,255,255,0.2);
    padding: 0.75rem 1rem;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    font-size: 1.1rem;
    min-width: 48px;
    min-height: 48px;
}

.nav-btn:hover {
    background: rgba(255,255,255,0.25);
    border-color: rgba(255,255,255,0.4);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.nav-btn:active {
    transform: translateY(0) scale(0.98);
}

.calendar-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 1rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    z-index: 2;
    position: relative;
    letter-spacing: -0.025em;
}

.calendar-title i {
    font-size: 2rem;
    opacity: 0.9;
}

.calendar-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
    z-index: 2;
    position: relative;
}

.calendar-actions .button {
    background: rgba(255,255,255,0.15);
    color: white;
    border: 2px solid rgba(255,255,255,0.2);
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.calendar-actions .button:hover {
    background: rgba(255,255,255,0.25);
    border-color: rgba(255,255,255,0.4);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.calendar-actions .primary-btn {
    background: var(--cal-accent);
    border-color: var(--cal-accent);
}

.calendar-actions .primary-btn:hover {
    background: #0891b2;
    border-color: #0891b2;
}

/* Calendar Grid */
.calendar-grid {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--cal-shadow);
    margin-bottom: 2rem;
    border: 1px solid var(--cal-border);
}

.calendar-weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    background: linear-gradient(135deg, var(--cal-dark) 0%, #334155 100%);
    color: white;
    position: relative;
}

.calendar-weekdays::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
}

.weekday {
    padding: 1.25rem 1rem;
    text-align: center;
    font-weight: 700;
    font-size: 0.875rem;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    position: relative;
}

.weekday::after {
    content: '';
    position: absolute;
    right: 0;
    top: 25%;
    bottom: 25%;
    width: 1px;
    background: rgba(255,255,255,0.1);
}

.weekday:last-child::after {
    display: none;
}

.calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background: var(--cal-border);
    padding: 1px;
}

.calendar-day {
    background: white;
    min-height: 160px;
    padding: 1rem;
    position: relative;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    border-radius: 0;
    overflow: hidden;
}

.calendar-day::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(99, 102, 241, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.calendar-day:hover::before {
    opacity: 1;
}

.calendar-day:hover {
    transform: translateY(-4px);
    z-index: 10;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

.calendar-day.empty {
    background: #f8fafc;
    cursor: default;
    opacity: 0.6;
}

.calendar-day.empty:hover {
    transform: none;
    box-shadow: none;
}

.calendar-day.today {
    background: linear-gradient(135deg, var(--cal-primary) 0%, var(--cal-secondary) 100%);
    color: white;
    box-shadow: 0 8px 32px rgba(99, 102, 241, 0.3);
    position: relative;
}

.calendar-day.today::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    animation: todayShimmer 2s infinite;
}

@keyframes todayShimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.calendar-day.today .day-number {
    color: white;
    font-weight: 800;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    z-index: 2;
    position: relative;
}

.calendar-day.has-entries {
    border-left: 4px solid var(--cal-accent);
    background: linear-gradient(135deg, #fff 0%, #f0f9ff 100%);
    position: relative;
}

.calendar-day.has-entries::before {
    background: linear-gradient(135deg, rgba(6, 182, 212, 0.05) 0%, rgba(99, 102, 241, 0.05) 100%);
}

.day-number {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--cal-dark);
    margin-bottom: 0.75rem;
    text-align: left;
    z-index: 2;
    position: relative;
    line-height: 1;
}

.day-entries {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.375rem;
    overflow: hidden;
    z-index: 2;
    position: relative;
}

.entry-indicator {
    font-size: 0.75rem;
    color: var(--cal-primary);
    background: rgba(99, 102, 241, 0.1);
    border: 1px solid rgba(99, 102, 241, 0.2);
    padding: 0.25rem 0.5rem;
    border-radius: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: all 0.2s ease;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.entry-indicator:hover {
    background: rgba(99, 102, 241, 0.15);
    border-color: rgba(99, 102, 241, 0.3);
    transform: translateX(2px);
}

.entry-indicator i {
    font-size: 0.625rem;
    opacity: 0.8;
}

.more-entries {
    font-size: 0.75rem;
    color: var(--cal-gray);
    font-weight: 600;
    text-align: center;
    margin-top: 0.25rem;
    padding: 0.25rem;
    background: rgba(100, 116, 139, 0.1);
    border-radius: 6px;
    z-index: 2;
    position: relative;
}

.day-actions {
    display: flex;
    gap: 0.375rem;
    margin-top: auto;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    justify-content: center;
    z-index: 3;
    position: relative;
    padding-top: 0.5rem;
}

.calendar-day:hover .day-actions {
    opacity: 1;
    transform: translateY(-2px);
}

.view-day, .add-entry {
    background: var(--cal-primary);
    color: white;
    border: none;
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.75rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 36px;
    min-height: 32px;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.2);
}

.view-day:hover {
    background: var(--cal-success);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.add-entry:hover {
    background: var(--cal-accent);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 12px rgba(6, 182, 212, 0.3);
}

/* Calendar Legend */
.calendar-legend {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
    margin-bottom: 1.5rem;
    padding: 1.5rem 2rem;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    box-shadow: var(--cal-shadow);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.875rem;
    color: var(--cal-dark);
    font-weight: 600;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.legend-item:hover {
    background: rgba(255, 255, 255, 0.8);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.legend-color {
    width: 20px;
    height: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    position: relative;
    overflow: hidden;
}

.legend-color::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.3) 50%, transparent 70%);
    animation: legendShimmer 3s infinite;
}

@keyframes legendShimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.today-color {
    background: linear-gradient(135deg, var(--cal-primary), var(--cal-secondary));
}

.has-entries-color {
    background: linear-gradient(135deg, var(--cal-accent), #0891b2);
}

.entry-indicator-sample {
    color: var(--cal-primary);
    font-size: 1rem;
    filter: drop-shadow(0 2px 4px rgba(99, 102, 241, 0.3));
}

/* Enhanced Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.7);
    backdrop-filter: blur(8px);
    animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: white;
    margin: 3% auto;
    padding: 0;
    border-radius: 24px;
    width: 90%;
    max-width: 800px;
    box-shadow: 0 32px 64px rgba(0,0,0,0.2);
    animation: modalSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    overflow: hidden;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-32px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    background: linear-gradient(135deg, var(--cal-primary) 0%, var(--cal-secondary) 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.modal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    animation: modalHeaderShimmer 3s infinite;
}

@keyframes modalHeaderShimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.modal-header h3 {
    margin: 0;
    font-size: 1.75rem;
    font-weight: 700;
    z-index: 2;
    position: relative;
}

.modal-body {
    padding: 2rem;
    max-height: 600px;
    overflow-y: auto;
    background: #fafafa;
}

.close-modal {
    font-size: 1.5rem;
    font-weight: bold;
    cursor: pointer;
    color: rgba(255,255,255,0.8);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 0.5rem;
    border-radius: 12px;
    z-index: 3;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.close-modal:hover {
    color: white;
    background: rgba(255,255,255,0.2);
    transform: rotate(90deg) scale(1.1);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .calendar-wrapper {
        margin: 1rem;
        padding: 1.5rem;
    }

    .calendar-header {
        padding: 1.25rem 1.5rem;
    }

    .calendar-title {
        font-size: 2.25rem;
    }
}

@media (max-width: 1024px) {
    .calendar-container {
        padding: 1.5rem 0.75rem;
    }

    .calendar-wrapper {
        padding: 1.25rem;
    }

    .calendar-header {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
        padding: 1.5rem;
    }

    .calendar-title {
        font-size: 2rem;
    }

    .calendar-navigation {
        order: 2;
    }

    .calendar-actions {
        order: 3;
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .calendar-container {
        padding: 1rem 0.5rem;
    }

    .calendar-wrapper {
        padding: 1rem;
        border-radius: 20px;
    }

    .calendar-header {
        padding: 1.25rem;
        border-radius: 16px;
    }

    .calendar-title {
        font-size: 1.75rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .calendar-day {
        min-height: 120px;
        padding: 0.75rem;
    }

    .day-number {
        font-size: 1.125rem;
        text-align: center;
        margin-bottom: 0.5rem;
    }

    .calendar-legend {
        gap: 1rem;
        padding: 1.25rem;
        flex-direction: column;
        align-items: center;
    }

    .legend-item {
        font-size: 0.8rem;
        padding: 0.375rem 0.75rem;
    }

    .weekday {
        padding: 1rem 0.75rem;
        font-size: 0.8rem;
    }

    .nav-btn {
        min-width: 44px;
        min-height: 44px;
        padding: 0.625rem 0.875rem;
    }

    .calendar-actions .button {
        padding: 0.625rem 1.25rem;
        font-size: 0.875rem;
    }
}

@media (max-width: 640px) {
    .calendar-header {
        padding: 1rem;
    }

    .calendar-title {
        font-size: 1.5rem;
    }

    .calendar-navigation {
        gap: 1rem;
    }

    .nav-btn {
        min-width: 40px;
        min-height: 40px;
        padding: 0.5rem 0.75rem;
        font-size: 1rem;
    }

    .calendar-day {
        min-height: 100px;
        padding: 0.5rem;
    }

    .day-number {
        font-size: 1rem;
        margin-bottom: 0.375rem;
    }

    .entry-indicator {
        font-size: 0.7rem;
        padding: 0.1875rem 0.375rem;
        gap: 0.1875rem;
    }

    .day-actions {
        gap: 0.25rem;
        padding-top: 0.375rem;
    }

    .view-day, .add-entry {
        padding: 0.375rem 0.5rem;
        font-size: 0.7rem;
        min-width: 32px;
        min-height: 28px;
    }

    .calendar-legend {
        padding: 1rem;
        gap: 0.75rem;
    }

    .legend-item {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        gap: 0.5rem;
    }

    .legend-color {
        width: 16px;
        height: 16px;
    }
}

@media (max-width: 480px) {
    .calendar-container {
        padding: 0.75rem 0.25rem;
    }

    .calendar-wrapper {
        padding: 0.75rem;
        border-radius: 16px;
    }

    .calendar-header {
        padding: 0.875rem;
        margin-bottom: 1.5rem;
    }

    .calendar-title {
        font-size: 1.375rem;
    }

    .calendar-day {
        min-height: 90px;
        padding: 0.375rem;
    }

    .day-number {
        font-size: 0.9rem;
        margin-bottom: 0.25rem;
    }

    .weekday {
        padding: 0.875rem 0.5rem;
        font-size: 0.75rem;
    }

    .entry-indicator {
        font-size: 0.65rem;
        padding: 0.125rem 0.25rem;
    }

    .more-entries {
        font-size: 0.65rem;
        padding: 0.1875rem;
    }

    .day-actions {
        opacity: 1; /* Always show on mobile */
        transform: none;
    }

    .view-day, .add-entry {
        padding: 0.25rem 0.375rem;
        font-size: 0.65rem;
        min-width: 28px;
        min-height: 24px;
    }

    .modal-content {
        width: 95%;
        margin: 5% auto;
    }

    .modal-header {
        padding: 1.25rem;
    }

    .modal-header h3 {
        font-size: 1.375rem;
    }

    .modal-body {
        padding: 1.25rem;
    }
}
