/* Reminders Page Styles */

/* Reminders Container */
.reminders-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: var(--space-6);
}

/* Reminders Header */
.reminders-header {
  text-align: center;
  margin-bottom: var(--space-8);
  padding: var(--space-8) 0;
  background: var(--gradient-bg-surface);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--border-primary);
  position: relative;
  overflow: hidden;
}

.reminders-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-warning);
  box-shadow: var(--glow-warning);
}

.reminders-header h1 {
  font-size: var(--font-size-4xl);
  background: var(--gradient-warning);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--space-4);
  font-weight: 800;
  animation: titleGlow 3s ease-in-out infinite alternate;
}

.reminders-header p {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* Reminders Actions */
.reminders-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
  padding: var(--space-4);
  background: var(--bg-surface);
  border-radius: var(--radius-xl);
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-md);
}

.reminders-filter {
  display: flex;
  gap: var(--space-2);
}

.filter-btn {
  padding: var(--space-2) var(--space-4);
  background: var(--bg-elevated);
  color: var(--text-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
  text-decoration: none;
}

.filter-btn:hover,
.filter-btn.active {
  background: var(--gradient-warning);
  color: var(--text-primary);
  border-color: transparent;
  box-shadow: var(--shadow-sm), var(--glow-warning);
}

.add-reminder-btn {
  padding: var(--space-3) var(--space-6);
  background: var(--gradient-warning);
  color: var(--text-primary);
  border: none;
  border-radius: var(--radius-lg);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-md), var(--glow-warning);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.add-reminder-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl), var(--glow-warning);
}

/* Reminders Grid */
.reminders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

/* Reminder Card */
.reminder-card {
  background: var(--bg-surface);
  border-radius: var(--radius-2xl);
  padding: var(--space-6);
  border: 1px solid var(--border-primary);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.reminder-card::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--gradient-warning);
  opacity: 0.7;
}

.reminder-card.urgent::before {
  background: var(--gradient-error);
  animation: urgentPulse 2s ease-in-out infinite;
}

.reminder-card.completed::before {
  background: var(--gradient-success);
}

@keyframes urgentPulse {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

.reminder-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-2xl), var(--glow-warning);
}

/* Reminder Header */
.reminder-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-4);
}

.reminder-priority {
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.reminder-priority.high {
  background: rgba(239, 68, 68, 0.2);
  color: var(--error-color);
  border: 1px solid var(--error-color);
}

.reminder-priority.medium {
  background: rgba(245, 158, 11, 0.2);
  color: var(--warning-color);
  border: 1px solid var(--warning-color);
}

.reminder-priority.low {
  background: rgba(34, 197, 94, 0.2);
  color: var(--success-color);
  border: 1px solid var(--success-color);
}

.reminder-status {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: var(--radius-full);
  border: 2px solid var(--border-primary);
  background: var(--bg-elevated);
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
}

.reminder-status.completed {
  background: var(--gradient-success);
  border-color: var(--success-color);
}

.reminder-status.completed::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--text-primary);
  font-size: var(--font-size-xs);
  font-weight: 700;
}

/* Reminder Content */
.reminder-title {
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  font-weight: 700;
  margin-bottom: var(--space-2);
  line-height: var(--leading-tight);
}

.reminder-description {
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
  margin-bottom: var(--space-4);
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.reminder-datetime {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--text-muted);
  font-size: var(--font-size-sm);
  margin-bottom: var(--space-4);
}

.reminder-datetime i {
  color: var(--warning-color);
}

.reminder-datetime.overdue {
  color: var(--error-color);
}

.reminder-datetime.overdue i {
  color: var(--error-color);
  animation: urgentBlink 1s ease-in-out infinite;
}

@keyframes urgentBlink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Reminder Actions */
.reminder-actions {
  display: flex;
  gap: var(--space-2);
  margin-top: auto;
}

.reminder-action-btn {
  flex: 1;
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  background: var(--bg-elevated);
  color: var(--text-secondary);
  text-decoration: none;
  text-align: center;
  font-size: var(--font-size-sm);
  font-weight: 500;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-1);
  cursor: pointer;
}

.reminder-action-btn:hover {
  background: var(--bg-overlay);
  color: var(--warning-color);
  border-color: var(--warning-color);
  transform: translateY(-1px);
}

.reminder-action-btn.edit:hover {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.reminder-action-btn.delete:hover {
  color: var(--error-color);
  border-color: var(--error-color);
}

/* Empty State */
.reminders-empty {
  text-align: center;
  padding: var(--space-12);
  background: var(--bg-surface);
  border-radius: var(--radius-2xl);
  border: 2px dashed var(--border-primary);
  margin: var(--space-8) 0;
}

.reminders-empty-icon {
  font-size: var(--font-size-6xl);
  color: var(--text-muted);
  margin-bottom: var(--space-4);
  opacity: 0.5;
}

.reminders-empty h3 {
  font-size: var(--font-size-2xl);
  color: var(--text-secondary);
  margin-bottom: var(--space-2);
}

.reminders-empty p {
  color: var(--text-muted);
  margin-bottom: var(--space-6);
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* Reminder Form Modal */
.reminder-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: var(--z-modal);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.reminder-modal.active {
  opacity: 1;
  visibility: visible;
}

.reminder-modal-content {
  background: var(--bg-surface);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-2xl);
  transform: scale(0.9) translateY(20px);
  transition: all var(--transition-normal);
}

.reminder-modal.active .reminder-modal-content {
  transform: scale(1) translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
  .reminders-container {
    padding: var(--space-4);
  }
  
  .reminders-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .reminders-actions {
    flex-direction: column;
    gap: var(--space-4);
  }
  
  .reminders-filter {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .reminders-header {
    padding: var(--space-6) var(--space-4);
  }
  
  .reminders-header h1 {
    font-size: var(--font-size-3xl);
  }
}

@media (max-width: 480px) {
  .reminder-card {
    padding: var(--space-4);
  }
  
  .reminder-actions {
    flex-direction: column;
  }
  
  .reminder-action-btn {
    flex: none;
  }
  
  .reminder-modal-content {
    padding: var(--space-6);
  }
}
