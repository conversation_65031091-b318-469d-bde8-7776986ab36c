/* Authentication Pages - Login & Signup */

/* Auth Container */
.auth-container {
  max-width: 450px;
  margin: 4rem auto;
  background: var(--bg-surface);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  box-shadow: var(--shadow-2xl), var(--glow-primary);
  border: 1px solid var(--border-primary);
  position: relative;
  overflow: hidden;
}

.auth-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
  box-shadow: var(--glow-primary);
}

.auth-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-mesh);
  opacity: 0.1;
  z-index: -1;
}

/* Auth Header */
.auth-header {
  text-align: center;
  margin-bottom: var(--space-8);
}

.auth-header h1 {
  font-size: var(--font-size-3xl);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--space-2);
  font-weight: 800;
}

.auth-header p {
  color: var(--text-secondary);
  font-size: var(--font-size-lg);
}

.auth-icon {
  font-size: var(--font-size-4xl);
  color: var(--primary-color);
  margin-bottom: var(--space-4);
  filter: drop-shadow(var(--glow-primary));
  animation: authIconFloat 3s ease-in-out infinite;
}

@keyframes authIconFloat {
  0%, 100% { transform: translateY(0px) scale(1); }
  50% { transform: translateY(-8px) scale(1.05); }
}

/* Auth Form */
.auth-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.auth-form-group {
  position: relative;
}

.auth-form-group label {
  display: block;
  margin-bottom: var(--space-2);
  color: var(--text-primary);
  font-weight: 600;
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.auth-form-group input {
  width: 100%;
  padding: var(--space-4);
  background: var(--bg-elevated);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-xl);
  color: var(--text-primary);
  font-size: var(--font-size-base);
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.auth-form-group input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: var(--shadow-lg), var(--glow-primary);
  transform: translateY(-2px);
}

.auth-form-group input::placeholder {
  color: var(--text-muted);
}

/* Input Icons */
.auth-form-group.with-icon {
  position: relative;
}

.auth-form-group.with-icon i {
  position: absolute;
  left: var(--space-4);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  font-size: var(--font-size-lg);
  transition: color var(--transition-normal);
}

.auth-form-group.with-icon input {
  padding-left: var(--space-12);
}

.auth-form-group.with-icon input:focus + i {
  color: var(--primary-color);
}

/* Auth Buttons */
.auth-btn {
  width: 100%;
  padding: var(--space-4);
  background: var(--gradient-primary);
  color: var(--text-primary);
  border: none;
  border-radius: var(--radius-xl);
  font-size: var(--font-size-lg);
  font-weight: 700;
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-lg), var(--glow-primary);
  position: relative;
  overflow: hidden;
}

.auth-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left var(--transition-slow);
}

.auth-btn:hover::before {
  left: 100%;
}

.auth-btn:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-2xl), var(--glow-primary);
}

.auth-btn:active {
  transform: translateY(-1px);
}

/* Secondary Auth Button */
.auth-btn-secondary {
  background: var(--bg-elevated);
  color: var(--text-primary);
  border: 2px solid var(--border-primary);
  box-shadow: var(--shadow-md);
}

.auth-btn-secondary:hover {
  background: var(--bg-overlay);
  border-color: var(--primary-color);
}

/* Auth Links */
.auth-links {
  text-align: center;
  margin-top: var(--space-6);
  padding-top: var(--space-6);
  border-top: 1px solid var(--border-primary);
}

.auth-links a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 600;
  transition: all var(--transition-normal);
  position: relative;
}

.auth-links a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--gradient-accent);
  transition: width var(--transition-normal);
}

.auth-links a:hover::after {
  width: 100%;
}

.auth-links a:hover {
  color: var(--primary-light);
  text-shadow: var(--glow-primary);
}

/* Error Messages */
.auth-error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid var(--error-color);
  color: var(--error-color);
  padding: var(--space-3);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-4);
  font-size: var(--font-size-sm);
  font-weight: 500;
  animation: errorShake 0.5s ease-in-out;
}

@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Success Messages */
.auth-success {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid var(--success-color);
  color: var(--success-color);
  padding: var(--space-3);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-4);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

/* Remember Me Checkbox */
.auth-checkbox {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin: var(--space-4) 0;
}

.auth-checkbox input[type="checkbox"] {
  width: auto;
  margin: 0;
  accent-color: var(--primary-color);
}

.auth-checkbox label {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  text-transform: none;
  letter-spacing: normal;
  font-weight: 400;
}

/* Responsive Design */
@media (max-width: 768px) {
  .auth-container {
    margin: var(--space-4);
    padding: var(--space-6);
  }
  
  .auth-header h1 {
    font-size: var(--font-size-2xl);
  }
  
  .auth-icon {
    font-size: var(--font-size-3xl);
  }
}

@media (max-width: 480px) {
  .auth-container {
    margin: var(--space-2);
    padding: var(--space-4);
  }
  
  .auth-form {
    gap: var(--space-4);
  }
  
  .auth-form-group input {
    padding: var(--space-3);
  }
  
  .auth-btn {
    padding: var(--space-3);
    font-size: var(--font-size-base);
  }
}
