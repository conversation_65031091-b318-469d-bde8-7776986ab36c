from django import template

register = template.Library()

@register.filter
def lookup(dictionary, key):
    """
    Template filter to lookup a value in a dictionary by key.
    Usage: {{ dict|lookup:key }}
    """
    if isinstance(dictionary, dict):
        return dictionary.get(key, [])
    return []

@register.filter
def add(value, arg):
    """Add the arg to the value."""
    try:
        return int(value) + int(arg)
    except (ValueError, TypeError):
        try:
            return value + arg
        except:
            return ''

@register.filter
def stringformat(value, format_string):
    """Format a value using Python string formatting."""
    try:
        return format_string % value
    except (TypeError, ValueError):
        return value

@register.filter
def split(value, delimiter=','):
    """Split a string by delimiter."""
    if isinstance(value, str):
        return [item.strip() for item in value.split(delimiter) if item.strip()]
    return []

@register.filter
def trim(value):
    """Remove leading and trailing whitespace."""
    if isinstance(value, str):
        return value.strip()
    return value
